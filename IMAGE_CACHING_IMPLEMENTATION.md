# Tenant-Specific Image Caching Implementation

## Overview

This document describes the implementation of tenant-specific image caching in the multi-tenant Telegram bot system. Each tenant (customer) has their own bot with unique file_ids, so caching must be isolated per tenant to ensure proper functionality.

## How It Works

### 1. Cache Storage
- **Location**: `cache/cache.json`
- **Format**: JSON file with tenant-specific nested structure
- **Structure**:
```json
{
  "tenant_name": {
    "image_type": {
      "image_path": "telegram_file_id",
      ...
    },
    ...
  },
  ...
}
```

### 2. Caching Flow

#### First Time Sending an Image:
1. <PERSON><PERSON> attempts to send image from disk (`assets/welcome.webp`)
2. Telegram returns a `Message` object with `photo` array
3. <PERSON><PERSON> extracts `file_id` from the largest photo size: `message.photo[-1].file_id`
4. <PERSON><PERSON> caches the `file_id` in `cache.json` mapped to the image path
5. Future sends use the cached `file_id` instead of uploading from disk

#### Subsequent Sends:
1. <PERSON><PERSON> checks cache for existing `file_id` for the image path
2. If found, attempts to send using cached `file_id`
3. If cached send succeeds, returns immediately (much faster)
4. If cached send fails (expired/invalid file_id), falls back to disk upload
5. Updates cache with new `file_id` from successful disk upload

### 3. Error Handling

#### Cache File Issues:
- If `cache.json` is corrupted or missing, creates new empty cache
- Graceful degradation - continues working without cache if needed

#### Invalid File IDs:
- Telegram file_ids can expire or become invalid
- Bot catches `BadRequest` errors and falls back to disk upload
- Automatically updates cache with new valid file_id

#### Network Issues:
- Timeout handling for both cached and disk-based sends
- Retry logic with exponential backoff
- Falls back to text-only message if all image attempts fail

## Benefits

### Performance Improvements:
- **Faster sends**: Cached file_ids send almost instantly vs. disk uploads
- **Reduced bandwidth**: No need to re-upload same images repeatedly
- **Lower server load**: Less file I/O and network traffic
- **Tenant isolation**: Each tenant's cache is independent and optimized

### User Experience:
- **Quicker responses**: Images appear faster in chat for all tenants
- **More reliable**: Fallback mechanisms ensure images are always sent
- **Consistent behavior**: Same images always work the same way per tenant
- **Multi-tenant support**: Each customer's bot performs optimally

### Multi-Tenant Specific Benefits:
- **Bot-specific file_ids**: Each tenant's bot has its own valid file_ids
- **Cache isolation**: One tenant's cache issues don't affect others
- **Scalable architecture**: Supports unlimited tenants in single cache file
- **Easy maintenance**: Clear separation makes debugging tenant-specific issues easier

## Implementation Details

### Key Methods Added:

#### `_ensure_cache_directory()`
Creates the `cache/` directory if it doesn't exist.

#### `_load_cache()` / `_save_cache()`
Handles reading/writing the cache.json file with error handling.

#### `_get_cached_file_id(image_path)`
Retrieves cached file_id for a given image path.

#### `_cache_file_id(image_type, image_path, file_id)`
Stores a new file_id in the cache.

#### `_send_with_cached_file_id()`
Attempts to send image using cached file_id with proper error handling.

### Modified Methods:

#### `send_message_with_image()`
Now passes `image_type` to enable caching.

#### `_send_with_image()`
Enhanced to:
1. Try cached file_id first
2. Fall back to disk upload if cache fails
3. Cache new file_id after successful disk upload

## Cache File Example

```json
{
  "indx": {
    "welcome": {
      "assets/welcome.webp": "BAADBAADAwADBREAAR4BAAFPVfq2AA"
    },
    "membership_offer": {
      "assets/membership_offer.webp": "BAADBAADBAEDBREAAR4BAAFPVfq3BB"
    },
    "account_number": {
      "assets/account_number_image.png": "BAADBAADBAEDBREAAR4BAAFPVfq5DD"
    }
  },
  "rishux": {
    "welcome": {
      "assets/welcome.webp": "BAADCCCCDDDDEEEEFFFFGGGGHHHHII"
    },
    "support": {
      "assets/support.webp": "BAADBAADBAEDBREAAR4BAAFPVfq4CC"
    },
    "account_number": {
      "assets/account_number_image.png": "BAADEEEEFFFFGGGGHHHHIIIIJJJJKK"
    }
  },
  "newcustomer": {
    "welcome": {
      "assets/welcome.webp": "BAADAAAABBBBCCCCDDDDEEEEFFFFGG"
    },
    "account_number": {
      "assets/account_number_image.png": "BAADLLLLMMMMNNNNOOOOPP"
    }
  }
}
```

**Note**: Each tenant has different file_ids for the same image because each tenant uses a different Telegram bot, and file_ids are bot-specific.

## Testing

### Test Scripts:
- **`test_image_cache.py`**: Basic cache functionality testing
- **`test_tenant_cache.py`**: Tenant-specific cache isolation testing
- **`test_images.py`**: Image availability and path resolution testing

### Running Tests:
```bash
python test_image_cache.py      # Basic cache functionality
python test_tenant_cache.py     # Tenant-specific caching
python test_images.py           # Image system compatibility
```

## Maintenance

### Cache Invalidation:
- File IDs are automatically invalidated by Telegram over time
- Bot handles this gracefully by falling back to disk upload
- No manual cache clearing needed

### Monitoring:
- Cache hits/misses are logged for monitoring
- File ID validation errors are logged as warnings
- Cache file corruption is logged as errors

## Backward Compatibility

- Fully backward compatible with existing code
- No changes required to existing bot logic
- Graceful degradation if caching fails
- All existing image sending methods work unchanged

## Future Enhancements

Potential improvements that could be added:
- Cache expiration timestamps
- Cache size limits
- Cache statistics/metrics
- Multi-tenant cache separation
- Cache warming on startup
