2025-05-27 08:38:52,353 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 08:38:52,353 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 08:38:52,353 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 08:38:52,353 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 08:38:57,510 - db.tenant_manager - WARNING - Failed to connect to MongoDB (attempt 1/5): mongodb:27017: [Errno 8] nodename nor servname provided, or not known (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68352cc465e5e5e89e2a62e0, topology_type: Unknown, servers: [<ServerDescription ('mongodb', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('mongodb:27017: [Errno 8] nodename nor servname provided, or not known (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-05-27 08:40:48,389 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 08:40:48,389 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 08:40:48,389 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 08:40:48,389 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 08:40:48,403 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 08:40:48,404 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:40:48,404 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 08:40:48,404 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 08:40:48,404 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 08:40:48,404 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:41:22,427 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 08:41:22,428 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 08:41:22,428 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 08:41:22,428 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 08:41:22,450 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 08:41:22,452 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:41:22,452 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 08:41:22,452 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 08:41:22,452 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 08:41:22,452 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:43:49,612 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 08:43:49,612 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 08:43:49,612 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 08:43:49,612 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 08:43:49,628 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 08:43:49,629 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:43:49,629 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 08:43:49,630 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 08:43:49,630 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 08:43:49,630 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:52:15,173 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 08:52:15,174 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 08:52:15,174 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 08:52:15,174 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 08:52:15,194 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 08:52:15,196 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:52:15,196 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 08:52:15,196 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 08:52:15,196 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 08:52:15,196 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:55:41,429 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 08:55:41,429 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 08:55:41,429 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 08:55:41,429 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 08:55:41,451 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 08:55:41,453 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 08:55:41,454 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 08:55:41,454 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 08:55:41,454 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 08:55:41,454 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:01:58,653 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:01:58,653 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:01:58,654 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:01:58,654 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:01:58,674 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:01:58,676 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:01:58,676 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:01:58,676 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:01:58,676 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:01:58,676 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:02:22,674 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:02:22,674 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:02:22,675 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:02:22,675 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:02:22,707 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:02:22,709 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:02:22,709 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:02:22,709 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:02:22,709 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:02:22,709 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:03:37,479 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:03:37,480 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:03:37,480 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:03:37,480 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:03:37,496 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:03:37,497 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:03:37,497 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:03:37,497 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:03:37,497 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:03:37,497 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:05:00,858 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:05:00,858 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:05:00,858 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:05:00,858 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:05:00,882 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:05:00,884 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:05:00,884 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:05:00,884 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:05:00,885 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:05:00,885 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:07:37,864 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:07:37,865 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:07:37,865 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:07:37,865 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:07:37,881 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:07:37,882 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:07:37,882 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:07:37,882 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:07:37,882 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:07:37,882 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:07:49,176 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:07:49,176 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:07:49,176 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:07:49,176 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:07:49,196 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:07:49,197 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:07:49,197 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:07:49,197 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:07:49,198 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:07:49,198 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:08:44,435 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:08:44,435 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:08:44,435 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:08:44,435 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:08:44,466 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:08:44,468 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:08:44,468 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:08:44,468 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:08:44,468 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:08:44,468 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:12:24,578 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:12:24,579 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:12:24,579 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:12:24,579 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:12:24,597 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:12:24,599 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:12:24,599 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:12:24,599 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:12:24,600 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:12:24,600 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:12:40,613 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:12:40,613 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:12:40,613 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:12:40,614 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:12:40,630 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:12:40,633 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:12:40,633 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:12:40,633 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:12:40,633 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:12:40,633 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:14:09,201 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:14:09,202 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:14:09,202 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:14:09,202 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:14:09,221 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:14:09,223 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:14:09,226 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:14:09,226 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:14:09,226 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:14:09,226 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:14:41,962 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:14:41,965 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:14:41,965 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:14:41,965 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:14:41,984 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:14:41,985 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:14:41,986 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:14:41,986 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:14:41,986 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:14:41,986 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:15:19,534 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:15:19,535 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:15:19,535 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:15:19,535 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:15:19,553 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:15:19,555 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:15:19,555 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:15:19,555 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:15:19,555 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:15:19,555 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:18:27,548 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 09:18:27,548 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 09:18:27,548 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 09:18:27,549 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 09:18:27,566 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 09:18:27,567 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 09:18:27,567 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 09:18:27,567 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 09:18:27,567 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 09:18:27,567 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 19:10:30,911 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 19:10:30,912 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 19:10:30,913 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 19:10:30,913 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 19:10:30,947 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 19:10:30,951 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 19:10:30,951 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 19:10:30,951 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 19:10:30,951 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 19:10:30,952 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 19:34:30,525 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 19:34:30,525 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 19:34:30,525 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 19:34:30,526 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 19:34:30,565 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 19:34:30,567 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 19:34:30,567 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 19:34:30,567 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 19:34:30,567 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 19:34:30,567 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-27 19:44:11,262 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-27 19:44:11,262 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-27 19:44:11,262 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-27 19:44:11,263 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-27 19:44:11,280 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-27 19:44:11,281 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-27 19:44:11,281 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-27 19:44:11,282 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-27 19:44:11,282 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-27 19:44:11,282 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:14:37,698 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 04:14:37,698 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 04:14:37,699 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 04:14:37,699 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 04:14:37,741 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 04:14:37,743 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:14:37,743 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 04:14:37,743 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 04:14:37,743 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 04:14:37,743 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:21:50,256 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 04:21:50,257 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 04:21:50,257 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 04:21:50,257 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 04:21:50,295 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 04:21:50,297 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:21:50,297 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 04:21:50,297 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 04:21:50,297 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 04:21:50,297 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:42:53,511 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 04:42:53,511 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 04:42:53,512 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 04:42:53,512 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 04:42:53,539 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 04:42:53,540 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:42:53,540 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 04:42:53,540 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 04:42:53,540 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 04:42:53,540 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:56:10,322 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 04:56:10,322 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 04:56:10,323 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 04:56:10,323 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 04:56:10,346 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 04:56:10,349 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 04:56:10,349 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 04:56:10,349 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 04:56:10,349 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 04:56:10,350 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:09:17,103 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:09:17,104 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:09:17,104 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:09:17,104 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:09:17,131 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:09:17,135 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:09:17,135 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:09:17,135 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:09:17,135 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:09:17,135 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:20:08,356 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:20:08,357 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:20:08,358 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:20:08,358 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:20:08,393 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:20:08,395 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:20:08,395 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:20:08,395 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:20:08,395 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:20:08,395 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:29:04,530 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:29:04,531 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:29:04,531 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:29:04,532 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:29:04,572 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:29:04,575 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:29:04,575 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:29:04,575 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:29:04,575 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:29:04,575 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:39:24,025 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:39:24,025 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:39:24,025 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:39:24,025 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:39:24,057 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:39:24,059 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:39:24,059 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:39:24,059 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:39:24,059 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:39:24,059 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:40:46,669 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:40:46,670 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:40:46,670 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:40:46,670 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:40:46,691 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:40:46,694 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:40:46,694 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:40:46,694 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:40:46,694 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:40:46,694 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:45:31,484 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:45:31,485 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:45:31,485 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:45:31,485 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:45:31,501 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:45:31,502 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:45:31,502 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:45:31,503 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:45:31,503 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:45:31,503 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:49:48,174 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:49:48,174 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:49:48,175 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:49:48,176 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:49:48,204 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:49:48,206 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:49:48,206 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:49:48,206 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:49:48,206 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:49:48,206 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:58:47,651 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 05:58:47,651 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 05:58:47,652 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 05:58:47,652 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 05:58:47,686 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 05:58:47,687 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 05:58:47,687 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 05:58:47,687 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 05:58:47,687 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 05:58:47,687 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:02:08,500 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 06:02:08,500 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 06:02:08,501 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 06:02:08,501 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 06:02:08,528 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 06:02:08,530 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:02:08,530 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 06:02:08,530 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 06:02:08,530 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 06:02:08,530 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:14:10,180 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 06:14:10,180 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 06:14:10,181 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 06:14:10,181 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 06:14:10,201 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 06:14:10,203 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:14:10,203 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 06:14:10,204 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 06:14:10,204 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 06:14:10,204 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:20:44,049 - __main__ - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 06:20:44,050 - __main__ - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 06:20:44,050 - src.multi_tenant_bot - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 06:20:44,050 - src.multi_tenant_bot - INFO - Discovering tenant databases...
2025-05-28 06:20:44,078 - db.tenant_manager - INFO - Successfully connected to MongoDB server
2025-05-28 06:20:44,080 - db.tenant_manager - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:20:44,080 - db.tenant_manager - INFO - Created connection to tenant database: indx_custdb
2025-05-28 06:20:44,080 - db.tenant_manager - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 06:20:44,080 - db.tenant_manager - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 06:20:44,080 - src.multi_tenant_bot - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:36:59,123 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 06:36:59,123 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 06:36:59,124 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 06:36:59,124 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 06:36:59,147 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 06:36:59,149 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:36:59,149 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 06:36:59,149 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 06:36:59,149 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 06:36:59,149 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:36:59,149 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 06:36:59,150 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 06:36:59,154 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 06:36:59,169 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 06:36:59,169 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 06:36:59,170 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 06:36:59,170 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 06:36:59,170 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 06:36:59,173 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 06:36:59,174 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 06:36:59,175 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 06:36:59,175 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 06:36:59,175 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 06:36:59,175 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 06:36:59,177 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 06:36:59,178 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 06:36:59,178 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 06:36:59,178 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 06:36:59,178 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 06:36:59,178 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 06:36:59,181 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 06:36:59,181 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 06:36:59,181 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:36:59,181 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:36:59,181 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:36:59,181 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 06:36:59,181 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 06:36:59,181 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 06:36:59,183 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 06:36:59,184 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 06:36:59,184 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:36:59,184 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:36:59,184 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:36:59,184 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 06:36:59,184 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 06:36:59,184 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 06:36:59,184 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 06:36:59,184 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 06:36:59,184 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 06:36:59,184 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 06:36:59,184 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 06:36:59,184 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 06:36:59,239 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 06:36:59,239 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 06:36:59,239 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 06:36:59,239 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 06:36:59,240 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 06:36:59,240 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 06:36:59,240 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 06:36:59,240 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 06:36:59,240 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 06:36:59,241 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 06:36:59,994 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 06:37:00,068 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 06:37:24,532 - __main__ - GLOBAL - INFO - Received interrupt signal. Shutting down...
2025-05-28 06:37:24,533 - multi_tenant_manager - GLOBAL - INFO - Stopping all bot instances...
2025-05-28 06:37:24,533 - multi_tenant_manager - GLOBAL - INFO - Stopping bot for tenant: indx
2025-05-28 06:53:28,636 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 06:53:28,636 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 06:53:28,637 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 06:53:28,637 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 06:53:28,657 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 06:53:28,659 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:53:28,659 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 06:53:28,659 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 06:53:28,659 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 06:53:28,659 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 06:53:28,659 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 06:53:28,660 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 06:53:28,666 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 06:53:28,689 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 06:53:28,689 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 06:53:28,689 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 06:53:28,689 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 06:53:28,690 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 06:53:28,692 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 06:53:28,693 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 06:53:28,694 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 06:53:28,694 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 06:53:28,694 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 06:53:28,694 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 06:53:28,697 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 06:53:28,698 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 06:53:28,698 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 06:53:28,698 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 06:53:28,699 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 06:53:28,699 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 06:53:28,703 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 06:53:28,704 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 06:53:28,704 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:53:28,704 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:53:28,704 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:53:28,704 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 06:53:28,704 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 06:53:28,704 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 06:53:28,707 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 06:53:28,707 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 06:53:28,707 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:53:28,707 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:53:28,707 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 06:53:28,708 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 06:53:28,708 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 06:53:28,708 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 06:53:28,708 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 06:53:28,708 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 06:53:28,709 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 06:53:28,709 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 06:53:28,710 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 06:53:28,710 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 06:53:28,784 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 06:53:28,784 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 06:53:28,785 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 06:53:28,785 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 06:53:28,786 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 06:53:28,786 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 06:53:28,786 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 06:53:28,787 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 06:53:28,788 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 06:53:28,788 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 06:53:29,603 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 06:53:29,604 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 06:53:37,061 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 06:53:37,061 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 06:53:37,062 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 06:53:37,062 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 06:53:37,062 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 06:53:37,062 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 06:53:38,696 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 06:53:56,032 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 06:53:56,034 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 06:53:56,034 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 06:53:56,034 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 06:53:56,034 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 06:53:56,035 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 06:54:00,439 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 06:55:17,919 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 06:55:17,922 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 06:55:17,923 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 06:55:17,923 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 06:55:17,923 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 06:55:17,924 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 06:55:19,456 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:21:32,149 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 07:21:32,150 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 07:21:32,150 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 07:21:32,150 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 07:21:32,171 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 07:21:32,173 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:21:32,173 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 07:21:32,173 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 07:21:32,173 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 07:21:32,173 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:21:32,174 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:21:32,174 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:21:32,174 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:21:32,180 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 07:21:32,198 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 07:21:32,198 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 07:21:32,198 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:21:32,198 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:21:32,198 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:21:32,199 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:21:32,201 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 07:21:32,202 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 07:21:32,203 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 07:21:32,203 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:21:32,204 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:21:32,204 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:21:32,204 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 07:21:32,208 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 07:21:32,209 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 07:21:32,209 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 07:21:32,209 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 07:21:32,210 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 07:21:32,210 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:21:32,214 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 07:21:32,215 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:21:32,215 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:21:32,215 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:21:32,215 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:21:32,215 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:21:32,215 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 07:21:32,215 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:21:32,218 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 07:21:32,218 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:21:32,218 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:21:32,218 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:21:32,218 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:21:32,218 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:21:32,218 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 07:21:32,219 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 07:21:32,219 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 07:21:32,219 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 07:21:32,220 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 07:21:32,221 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 07:21:32,222 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 07:21:32,222 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 07:21:32,303 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 07:21:32,303 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 07:21:32,303 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:21:32,304 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:21:32,305 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:21:32,305 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 07:21:32,305 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 07:21:32,306 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:21:32,306 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 07:21:32,307 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 07:21:33,534 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:21:33,549 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:21:45,554 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:21:45,555 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:21:45,555 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:21:45,555 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 07:21:45,600 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded and cached config for customer: indx
2025-05-28 07:21:45,601 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:21:45,601 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:21:45,601 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:21:46,725 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:21:57,456 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:21:57,458 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:21:57,458 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:21:57,459 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:21:57,460 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:21:57,460 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:21:58,941 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:22:03,736 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 07:22:03,737 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 07:22:03,737 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 07:22:03,737 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 07:22:03,769 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 07:22:03,771 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:22:03,771 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 07:22:03,772 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 07:22:03,772 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 07:22:03,772 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:22:03,772 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:22:03,773 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:22:03,773 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:22:03,780 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 07:22:03,800 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 07:22:03,801 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 07:22:03,801 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:22:03,801 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:22:03,801 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:22:03,802 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:22:03,805 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 07:22:03,806 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 07:22:03,806 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 07:22:03,806 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:22:03,807 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:22:03,807 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:22:03,807 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 07:22:03,811 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 07:22:03,812 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 07:22:03,813 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 07:22:03,813 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 07:22:03,813 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 07:22:03,813 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:22:03,816 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 07:22:03,816 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:22:03,816 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:03,817 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:03,817 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:03,817 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:22:03,817 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 07:22:03,817 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:22:03,820 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 07:22:03,820 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:22:03,820 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:03,820 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:03,820 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:03,820 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:22:03,820 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 07:22:03,820 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 07:22:03,821 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 07:22:03,821 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 07:22:03,821 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 07:22:03,822 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 07:22:03,822 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 07:22:03,822 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 07:22:03,893 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 07:22:03,893 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 07:22:03,894 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:22:03,894 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:22:03,895 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:22:03,895 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 07:22:03,895 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 07:22:03,895 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:22:03,897 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 07:22:03,897 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 07:22:04,559 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:22:04,559 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:22:06,300 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:22:06,301 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:06,302 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:22:06,303 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 07:22:06,345 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded and cached config for customer: indx
2025-05-28 07:22:06,345 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:06,346 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:22:06,346 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:22:07,155 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:22:15,716 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:22:15,717 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:15,717 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:22:15,718 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:15,718 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:22:15,719 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:22:17,441 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:22:19,466 - __main__ - GLOBAL - INFO - Received interrupt signal. Shutting down...
2025-05-28 07:22:19,467 - multi_tenant_manager - GLOBAL - INFO - Stopping all bot instances...
2025-05-28 07:22:19,467 - multi_tenant_manager - GLOBAL - INFO - Stopping bot for tenant: indx
2025-05-28 07:22:25,917 - telegram_bot.indx - GLOBAL - INFO - Bot stopped by user
2025-05-28 07:22:25,922 - telegram_bot.indx - GLOBAL - INFO - Bot task was cancelled
2025-05-28 07:22:25,922 - telegram.ext.Application - GLOBAL - INFO - Application is stopping. This might take a moment.
2025-05-28 07:22:25,924 - telegram.ext.Application - GLOBAL - INFO - Application.stop() complete
2025-05-28 07:22:25,926 - multi_tenant_manager - GLOBAL - INFO - Bot thread for tenant indx stopped successfully
2025-05-28 07:22:25,926 - multi_tenant_manager - GLOBAL - INFO - Stopping bot for tenant: newcustomer
2025-05-28 07:22:25,933 - telegram_bot.newcustomer - GLOBAL - INFO - Bot stopped by user
2025-05-28 07:22:25,933 - telegram_bot.newcustomer - GLOBAL - INFO - Bot task was cancelled
2025-05-28 07:22:25,933 - telegram.ext.Application - GLOBAL - INFO - Application is stopping. This might take a moment.
2025-05-28 07:22:25,945 - multi_tenant_manager - GLOBAL - INFO - Bot thread for tenant newcustomer stopped successfully
2025-05-28 07:22:25,945 - __main__ - GLOBAL - INFO - Shutdown complete
2025-05-28 07:22:26,972 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-2' coro=<TelegramBot.run_async() running at /Users/<USER>/Documents/aws/project/telegram_bot/src/bot.py:180> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-05-28 07:22:26,972 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Application:**********:update_fetcher' coro=<Application._update_fetcher() running at /Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_application.py:1221> wait_for=<Future finished result=None>>
2025-05-28 07:22:26,973 - asyncio - GLOBAL - ERROR - Task exception was never retrieved
future: <Task finished name='Updater:start_polling:polling_task' coro=<network_retry_loop() done, defined at /Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_utils/networkloop.py:44> exception=KeyboardInterrupt()>
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py", line 982, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/bot.py", line 210, in run
    loop.run_until_complete(task)
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py", line 641, in run_until_complete
    self.run_forever()
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py", line 608, in run_forever
    self._run_once()
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py", line 1936, in _run_once
    handle._run()
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_extbot.py", line 658, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 4593, in get_updates
    await self._post(
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 691, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_extbot.py", line 362, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 720, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_httpxrequest.py", line 277, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/http11.py", line 88, in handle_async_request
    await self._send_request_body(**kwargs)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/http11.py", line 159, in _send_request_body
    await self._send_event(event, timeout=timeout)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_async/http11.py", line 166, in _send_event
    await self._network_stream.write(bytes_to_send, timeout=timeout)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/httpcore/_backends/anyio.py", line 50, in write
    await self._stream.send(item=buffer)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/anyio/streams/tls.py", line 226, in send
    await self._call_sslobject_method(self._ssl_object.write, item)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/anyio/streams/tls.py", line 192, in _call_sslobject_method
    await self.transport_stream.send(self._write_bio.read())
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 1291, in send
    self._transport.write(item)
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/selector_events.py", line 1061, in write
    n = self._sock.send(data)
        ^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
2025-05-28 07:22:26,980 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Updater:start_polling:polling_task' coro=<network_retry_loop() running at /Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_utils/networkloop.py:115> wait_for=<Future finished result=None>>
2025-05-28 07:22:26,981 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-30' coro=<Event.wait() done, defined at /opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py:200> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-05-28 07:22:26,981 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-33' coro=<Event.wait() done, defined at /opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py:200> wait_for=<Future cancelled>>
2025-05-28 07:22:34,817 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 07:22:34,817 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 07:22:34,818 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 07:22:34,818 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 07:22:34,843 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 07:22:34,846 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:22:34,846 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 07:22:34,846 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 07:22:34,846 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 07:22:34,846 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:22:34,847 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:22:34,847 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:22:34,847 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:22:34,854 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 07:22:34,869 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 07:22:34,870 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 07:22:34,870 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:22:34,870 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:22:34,870 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:22:34,871 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:22:34,874 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 07:22:34,875 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 07:22:34,876 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 07:22:34,876 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:22:34,877 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:22:34,877 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:22:34,877 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 07:22:34,880 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 07:22:34,881 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 07:22:34,881 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 07:22:34,881 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 07:22:34,881 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 07:22:34,881 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:22:34,885 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 07:22:34,885 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:22:34,885 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:34,885 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:34,885 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:34,885 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:22:34,885 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 07:22:34,885 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:22:34,889 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 07:22:34,889 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:22:34,889 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:34,889 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:34,889 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:22:34,889 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:22:34,889 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 07:22:34,889 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 07:22:34,889 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 07:22:34,890 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 07:22:34,891 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 07:22:34,891 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 07:22:34,892 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 07:22:34,892 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 07:22:34,964 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 07:22:34,964 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 07:22:34,964 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:22:34,964 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:22:34,965 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:22:34,966 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 07:22:34,966 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 07:22:34,966 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:22:34,967 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 07:22:34,968 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 07:22:35,506 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:22:35,532 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:22:38,349 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:22:38,350 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:38,350 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:22:38,351 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 07:22:38,387 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded and cached config for customer: indx
2025-05-28 07:22:38,388 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:38,388 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:22:38,388 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:22:39,172 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:22:46,362 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:22:46,363 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:46,363 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:22:46,364 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:22:46,364 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:22:46,365 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:22:48,812 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:23:01,645 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:23:01,646 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:23:01,646 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:23:01,646 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:23:01,647 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:23:01,647 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:23:03,194 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:23:30,914 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:23:30,916 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:23:30,916 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:23:30,917 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:23:30,917 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:23:30,917 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:23:32,184 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:23:37,731 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 07:23:37,732 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 07:23:37,732 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 07:23:37,732 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 07:23:37,749 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 07:23:37,751 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:23:37,751 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 07:23:37,751 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 07:23:37,751 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 07:23:37,751 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:23:37,751 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:23:37,752 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:23:37,752 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:23:37,758 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 07:23:37,774 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 07:23:37,774 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 07:23:37,774 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:23:37,775 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:23:37,775 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:23:37,775 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:23:37,780 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 07:23:37,782 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 07:23:37,782 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 07:23:37,782 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:23:37,782 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:23:37,782 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:23:37,782 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 07:23:37,787 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 07:23:37,788 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 07:23:37,788 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 07:23:37,789 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 07:23:37,789 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 07:23:37,789 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:23:37,793 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 07:23:37,793 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:23:37,793 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:23:37,793 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:23:37,793 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:23:37,793 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:23:37,794 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 07:23:37,794 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:23:37,798 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 07:23:37,798 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:23:37,798 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:23:37,798 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:23:37,798 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:23:37,798 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:23:37,799 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 07:23:37,799 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 07:23:37,799 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 07:23:37,799 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 07:23:37,800 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 07:23:37,800 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 07:23:37,800 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 07:23:37,800 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 07:23:37,873 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 07:23:37,873 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 07:23:37,873 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:23:37,873 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:23:37,875 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:23:37,875 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 07:23:37,875 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 07:23:37,876 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:23:37,877 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 07:23:37,877 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 07:23:38,482 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:23:38,491 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:23:40,169 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:23:40,170 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:23:40,170 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:23:40,170 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 07:23:40,211 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded and cached config for customer: indx
2025-05-28 07:23:40,211 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:23:40,211 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:23:40,212 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:23:41,051 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:23:49,457 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:23:49,523 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:23:49,524 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-28 07:23:49,527 - telegram_bot.indx - GLOBAL - INFO - Retrieved broker referral link from MongoDB: https://one.loki.org/a/ljekbvt0qm
2025-05-28 07:23:49,527 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-28 07:23:49,527 - telegram_bot.indx - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-28 07:23:49,527 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-28 07:23:49,527 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-28 07:23:49,528 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:23:50,665 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:23:50,667 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:23:54,991 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:23:55,030 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:23:55,030 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: account_created
2025-05-28 07:23:55,031 - telegram_bot.indx - GLOBAL - INFO - account_created callback for user *********
2025-05-28 07:23:55,031 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:23:55,035 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-28 07:23:55,038 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-28 07:23:55,039 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-28 07:23:55,039 - telegram_bot.indx - GLOBAL - INFO - User data for *********: None
2025-05-28 07:23:55,039 - telegram_bot.indx - GLOBAL - INFO - No user data found for user *********
2025-05-28 07:23:55,039 - telegram_bot.indx - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-28 07:23:55,040 - telegram_bot.indx - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-28 07:23:55,040 - telegram_bot.indx - GLOBAL - INFO - Account number image found at assets/account_number_image.png
2025-05-28 07:23:55,490 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:25:29,626 - telegram_bot.indx - GLOBAL - INFO - Received account number from user *********: *********
2025-05-28 07:25:29,628 - telegram_bot.indx - GLOBAL - INFO - Stored account number in context for user *********: *********
2025-05-28 07:25:29,641 - telegram_bot.indx - GLOBAL - INFO - Access code exists in access_code collection: True
2025-05-28 07:25:29,648 - telegram_bot.indx - GLOBAL - INFO - Found 0 active users with access code *********: []
2025-05-28 07:25:29,651 - telegram_bot.indx - GLOBAL - INFO - Account used by other check: [], existing users: []
2025-05-28 07:25:29,651 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:25:29,663 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-28 07:25:29,667 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-28 07:25:29,667 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-28 07:25:29,667 - telegram_bot.indx - GLOBAL - INFO - Is expired user check: None
2025-05-28 07:25:29,667 - telegram_bot.indx - GLOBAL - INFO - Account exists and is valid for user *********: True
2025-05-28 07:25:29,667 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:25:29,671 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-28 07:25:29,673 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-28 07:25:29,673 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-28 07:25:33,205 - telegram_bot.indx - GLOBAL - INFO - Received name from user *********: lokendar
2025-05-28 07:25:33,206 - telegram_bot.indx - GLOBAL - INFO - Stored name in context for user *********: lokendar
2025-05-28 07:25:37,175 - telegram_bot.indx - GLOBAL - INFO - User ********* provided email: <EMAIL>
2025-05-28 07:25:40,692 - telegram_bot.indx - GLOBAL - INFO - User ********* provided WhatsApp: +**********
2025-05-28 07:25:42,791 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:25:42,841 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:25:42,842 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: pro_verification
2025-05-28 07:25:42,842 - telegram_bot.indx - GLOBAL - INFO - User ********* selected trading experience for verification: pro
2025-05-28 07:25:42,843 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:25:42,848 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-28 07:25:42,853 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-28 07:25:42,853 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-28 07:25:42,863 - telegram_bot.indx - GLOBAL - INFO - Access code exists in access_code collection: True
2025-05-28 07:25:42,866 - telegram_bot.indx - GLOBAL - INFO - Found 0 active users with access code *********: []
2025-05-28 07:25:42,867 - telegram_bot.indx - GLOBAL - INFO - Existing users with access code *********: []
2025-05-28 07:25:42,867 - telegram_bot.indx - GLOBAL - INFO - Code used by other check: [], existing users: []
2025-05-28 07:25:42,867 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:25:42,871 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-28 07:25:42,872 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-28 07:25:42,872 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-28 07:25:42,872 - telegram_bot.indx - GLOBAL - INFO - Is expired user check: None
2025-05-28 07:25:42,872 - telegram_bot.indx - GLOBAL - INFO - Access code is valid for user *********: True
2025-05-28 07:25:42,873 - telegram_bot.indx - GLOBAL - INFO - Saving user data to master_user_data: {'user_id': *********, 'access_code': '*********', 'name': 'lokendar', 'email': '<EMAIL>', 'whatsapp': '+**********', 'trading_experience': 'pro', 'subscription': 'unknown', 'user_verify': False, 'broker_reg': False, 'user_status': 'verification_pending', 'user_verify_status': 'pending', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873111, tzinfo=datetime.timezone.utc), 'telegram_bot_id': **********}
2025-05-28 07:25:42,879 - telegram_bot.indx - GLOBAL - INFO - Save result: modified_count=0, upserted_id=68366d1edce8df7299d487f4, success=True
2025-05-28 07:25:42,881 - telegram_bot.indx - GLOBAL - INFO - Found 1 active users with access code *********: [*********]
2025-05-28 07:25:42,882 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:25:42,883 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: {'_id': ObjectId('68366d1edce8df7299d487f4'), 'user_id': *********, 'access_code': '*********', 'broker_reg': False, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'unknown', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'incomplete', 'user_verify': False, 'user_verify_status': 'pending', 'whatsapp': '+**********'}
2025-05-28 07:25:42,883 - telegram_bot.indx - GLOBAL - INFO - Returning user data for user_id *********: {'_id': '68366d1edce8df7299d487f4', 'user_id': *********, 'access_code': '*********', 'broker_reg': False, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'unknown', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'incomplete', 'user_verify': False, 'user_verify_status': 'pending', 'whatsapp': '+**********'}
2025-05-28 07:25:42,884 - telegram_bot.indx - GLOBAL - INFO - Access code ********* is valid. Automatically verifying user *********
2025-05-28 07:25:42,885 - telegram_bot.indx - GLOBAL - INFO - Found 1 active users with access code *********: [*********]
2025-05-28 07:25:42,885 - telegram_bot.indx - GLOBAL - INFO - Existing users with access code *********: [*********]
2025-05-28 07:25:42,885 - telegram_bot.indx - GLOBAL - INFO - Code used by other check: False, existing users: [*********]
2025-05-28 07:25:42,888 - telegram_bot.indx - GLOBAL - INFO - Successfully verified user ********* with access code *********
2025-05-28 07:25:42,888 - telegram_bot.indx - GLOBAL - INFO - Successfully saved verification data for user ********* with access code *********
2025-05-28 07:25:42,888 - telegram_bot.indx - GLOBAL - INFO - Attempting to create invite link for chat_id: -1002682842201
2025-05-28 07:25:43,312 - telegram_bot.indx - GLOBAL - INFO - Created one-time invite link: https://t.me/+l2n0_USl0WAwZmE1
2025-05-28 07:25:44,565 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:26:33,115 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:26:33,152 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:26:33,152 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-28 07:26:33,153 - telegram_bot.indx - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-28 07:26:33,154 - telegram_bot.indx - GLOBAL - INFO - Editing text message (attempt 1)
2025-05-28 07:26:33,519 - telegram_bot.indx - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-28 07:26:33,520 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:26:38,050 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 07:26:38,050 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 07:26:38,051 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 07:26:38,051 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 07:26:38,091 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 07:26:38,093 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:26:38,093 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 07:26:38,093 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 07:26:38,093 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 07:26:38,093 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:26:38,094 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:26:38,094 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:26:38,094 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:26:38,100 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 07:26:38,113 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 07:26:38,113 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 07:26:38,113 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:26:38,114 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:26:38,114 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:26:38,114 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:26:38,117 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 07:26:38,118 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 07:26:38,119 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 07:26:38,119 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:26:38,119 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:26:38,119 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:26:38,119 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 07:26:38,122 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 07:26:38,123 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 07:26:38,124 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 07:26:38,124 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 07:26:38,124 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 07:26:38,124 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:26:38,129 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 07:26:38,130 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:26:38,130 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:26:38,130 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:26:38,130 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:26:38,130 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:26:38,130 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 07:26:38,130 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:26:38,134 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 07:26:38,134 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:26:38,134 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:26:38,134 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:26:38,134 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:26:38,134 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:26:38,134 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 07:26:38,134 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 07:26:38,134 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 07:26:38,135 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 07:26:38,137 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 07:26:38,137 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 07:26:38,137 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 07:26:38,137 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 07:26:38,220 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 07:26:38,220 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 07:26:38,221 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:26:38,221 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:26:38,222 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:26:38,222 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 07:26:38,222 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 07:26:38,222 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:26:38,223 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 07:26:38,224 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 07:26:38,849 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:26:38,900 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:26:43,127 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:26:43,127 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:26:43,128 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:26:43,128 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 07:26:43,171 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded and cached config for customer: indx
2025-05-28 07:26:43,172 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:26:43,172 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:26:43,172 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:26:45,457 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:27:05,322 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:27:05,388 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:27:05,389 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-28 07:27:05,390 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-28 07:27:05,390 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-28 07:27:05,391 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:27:06,424 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:27:06,424 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:27:10,970 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:27:10,992 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:27:10,992 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: reg_want_to_join
2025-05-28 07:27:10,993 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:27:10,996 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: {'_id': ObjectId('68366d1edce8df7299d487f4'), 'user_id': *********, 'access_code': '*********', 'broker_reg': True, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'active', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'active', 'user_verify': True, 'user_verify_status': 'approved', 'whatsapp': '+**********', 'added_at': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000)}
2025-05-28 07:27:10,996 - telegram_bot.indx - GLOBAL - INFO - Returning user data for user_id *********: {'_id': '68366d1edce8df7299d487f4', 'user_id': *********, 'access_code': '*********', 'broker_reg': True, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'active', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'active', 'user_verify': True, 'user_verify_status': 'approved', 'whatsapp': '+**********', 'added_at': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000)}
2025-05-28 07:27:10,997 - telegram_bot.indx - GLOBAL - INFO - User data for *********: {'_id': '68366d1edce8df7299d487f4', 'user_id': *********, 'access_code': '*********', 'broker_reg': True, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'active', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'active', 'user_verify': True, 'user_verify_status': 'approved', 'whatsapp': '+**********', 'added_at': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000)}
2025-05-28 07:27:10,997 - telegram_bot.indx - GLOBAL - INFO - Subscription status for user *********: active, user_verify: True, broker_reg: True, user_status: active
2025-05-28 07:27:10,997 - telegram_bot.indx - GLOBAL - INFO - User ********* has active subscription and is verified
2025-05-28 07:27:10,997 - telegram_bot.indx - GLOBAL - INFO - Subscription check result for user *********: is_verified=True, status=active, status_message=None
2025-05-28 07:27:10,998 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:27:11,001 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: {'_id': ObjectId('68366d1edce8df7299d487f4'), 'user_id': *********, 'access_code': '*********', 'broker_reg': True, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'active', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'active', 'user_verify': True, 'user_verify_status': 'approved', 'whatsapp': '+**********', 'added_at': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000)}
2025-05-28 07:27:11,001 - telegram_bot.indx - GLOBAL - INFO - Returning user data for user_id *********: {'_id': '68366d1edce8df7299d487f4', 'user_id': *********, 'access_code': '*********', 'broker_reg': True, 'email': '<EMAIL>', 'last_updated': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000), 'name': 'lokendar', 'notification_history': '', 'registration_time': datetime.datetime(2025, 5, 28, 1, 55, 42, 873000), 'subscription': 'active', 'telegram_bot_id': **********, 'trading_experience': 'pro', 'user_status': 'active', 'user_verify': True, 'user_verify_status': 'approved', 'whatsapp': '+**********', 'added_at': datetime.datetime(2025, 5, 28, 1, 55, 42, 885000)}
2025-05-28 07:27:11,002 - telegram_bot.indx - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-28 07:27:11,395 - telegram_bot.indx - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-28 07:27:11,395 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:27:13,445 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:27:13,466 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:27:13,467 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-28 07:27:13,467 - telegram_bot.indx - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-28 07:27:13,467 - telegram_bot.indx - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-28 07:27:13,871 - telegram_bot.indx - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-28 07:27:13,872 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:27:23,550 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:27:23,608 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:27:23,609 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-28 07:27:23,612 - telegram_bot.indx - GLOBAL - INFO - Retrieved broker referral link from MongoDB: https://one.loki.org/a/ljekbvt0qm
2025-05-28 07:27:23,612 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-28 07:27:23,613 - telegram_bot.indx - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-28 07:27:23,613 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-28 07:27:23,613 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-28 07:27:23,614 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:27:28,883 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:27:28,885 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:28:29,616 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:28:29,676 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:28:29,677 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-28 07:28:29,677 - telegram_bot.indx - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-28 07:28:29,678 - telegram_bot.indx - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-28 07:28:30,090 - telegram_bot.indx - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-28 07:28:30,091 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:28:35,060 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:28:35,084 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:28:35,084 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: verify_membership
2025-05-28 07:28:35,085 - telegram_bot.indx - GLOBAL - INFO - verify_membership image found at assets/verify_membership.webp
2025-05-28 07:28:35,086 - telegram_bot.indx - GLOBAL - INFO - Replacing message with verify_membership image
2025-05-28 07:28:35,086 - telegram_bot.indx - GLOBAL - INFO - verify_membership image found at assets/verify_membership.webp
2025-05-28 07:28:35,086 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/verify_membership.webp
2025-05-28 07:28:35,086 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:28:41,132 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:28:41,132 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:29:01,799 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:29:01,834 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:29:01,834 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-28 07:29:01,835 - telegram_bot.indx - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-28 07:29:01,835 - telegram_bot.indx - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-28 07:29:02,242 - telegram_bot.indx - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-28 07:29:02,242 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:38:29,013 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:38:29,061 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:38:29,061 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-28 07:38:29,062 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-28 07:38:29,063 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-28 07:38:29,063 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:38:36,773 - telegram_bot.indx - GLOBAL - WARNING - Image send timeout/network error (attempt 1), retrying in 3s: Timed out
2025-05-28 07:38:39,776 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 2)...
2025-05-28 07:38:43,448 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 2)
2025-05-28 07:38:43,449 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:41:02,559 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 07:41:02,559 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 07:41:02,560 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 07:41:02,560 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 07:41:02,579 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 07:41:02,581 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:41:02,581 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 07:41:02,581 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 07:41:02,581 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 07:41:02,581 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 07:41:02,583 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:41:02,583 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:41:02,584 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:41:02,589 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 07:41:02,621 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 07:41:02,622 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 07:41:02,622 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:41:02,624 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:41:02,624 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:41:02,624 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:41:02,633 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 07:41:02,635 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 07:41:02,635 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 07:41:02,635 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 07:41:02,636 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 07:41:02,636 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 07:41:02,636 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 07:41:02,642 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 07:41:02,643 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 07:41:02,643 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 07:41:02,643 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 07:41:02,643 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 07:41:02,643 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 07:41:02,647 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 07:41:02,648 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:41:02,648 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:41:02,648 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:41:02,648 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:41:02,648 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:41:02,648 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 07:41:02,648 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 07:41:02,651 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 07:41:02,651 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 07:41:02,652 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:41:02,652 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:41:02,652 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 07:41:02,652 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 07:41:02,652 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 07:41:02,652 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 07:41:02,652 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 07:41:02,652 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 07:41:02,654 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 07:41:02,654 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 07:41:02,654 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 07:41:02,655 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 07:41:02,738 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 07:41:02,738 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 07:41:02,738 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:41:02,738 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 07:41:02,740 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:41:02,740 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 07:41:02,740 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 07:41:02,741 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 07:41:02,741 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 07:41:02,742 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 07:41:03,737 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:41:03,932 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 07:41:07,026 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:41:07,027 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:41:07,028 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:41:07,028 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 07:41:07,052 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded and cached config for customer: indx
2025-05-28 07:41:07,053 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:41:07,053 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:41:07,053 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:41:08,279 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:41:09,949 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:41:09,980 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:41:09,980 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-28 07:41:09,984 - telegram_bot.indx - GLOBAL - INFO - Retrieved broker referral link from MongoDB: https://one.loki.org/a/ljekbvt0qm
2025-05-28 07:41:09,985 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-28 07:41:09,985 - telegram_bot.indx - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-28 07:41:09,986 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-28 07:41:09,986 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-28 07:41:09,986 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:41:11,896 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:41:11,897 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:45:56,539 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 07:45:56,542 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:45:56,544 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 07:45:56,544 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 07:45:56,544 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 07:45:56,545 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:45:59,578 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:46:01,835 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:46:01,866 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:46:01,866 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-28 07:46:01,867 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-28 07:46:01,867 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-28 07:46:01,867 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:46:05,216 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:46:05,217 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 07:46:07,494 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 07:46:07,516 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 07:46:07,517 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: reg_want_to_join
2025-05-28 07:46:07,517 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-28 07:46:07,520 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-28 07:46:07,522 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-28 07:46:07,522 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-28 07:46:07,523 - telegram_bot.indx - GLOBAL - INFO - User data for *********: None
2025-05-28 07:46:07,523 - telegram_bot.indx - GLOBAL - INFO - No user data found for user *********
2025-05-28 07:46:07,523 - telegram_bot.indx - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-28 07:46:07,524 - telegram_bot.indx - GLOBAL - INFO - User ********* has status: new, proceeding with join steps
2025-05-28 07:46:07,527 - telegram_bot.indx - GLOBAL - INFO - Retrieved broker referral link from MongoDB: https://one.loki.org/a/ljekbvt0qm
2025-05-28 07:46:07,528 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-28 07:46:07,528 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-28 07:46:07,528 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 07:46:10,269 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 07:46:10,308 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 08:26:30,940 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 08:26:30,940 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 08:26:30,941 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 08:26:30,941 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 08:26:30,976 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 08:26:30,978 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:26:30,979 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 08:26:30,979 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 08:26:30,979 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 08:26:30,979 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:26:30,979 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:26:30,980 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:26:30,980 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:26:30,984 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 08:26:31,021 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 08:26:31,021 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 08:26:31,021 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:26:31,021 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:26:31,022 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:26:31,022 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:26:31,030 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 08:26:31,032 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 08:26:31,033 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 08:26:31,033 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:26:31,033 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:26:31,033 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:26:31,033 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 08:26:31,037 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 08:26:31,039 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 08:26:31,039 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 08:26:31,039 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 08:26:31,039 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 08:26:31,039 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:26:31,042 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 08:26:31,042 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:26:31,042 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:31,042 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:31,043 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:31,043 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:26:31,043 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 08:26:31,043 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:26:31,047 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 08:26:31,047 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:26:31,047 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:31,048 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:31,048 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:31,048 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:26:31,049 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 08:26:31,049 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 08:26:31,049 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 08:26:31,049 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 08:26:31,050 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 08:26:31,050 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 08:26:31,051 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 08:26:31,051 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 08:26:31,130 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 08:26:31,131 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:26:31,132 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 08:26:31,133 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:26:31,133 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:26:31,134 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 08:26:31,135 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 08:26:31,134 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:26:31,135 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 08:26:31,137 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 08:26:31,726 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:26:31,867 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:26:37,615 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 08:26:37,616 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:26:37,616 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 08:26:37,616 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:26:37,648 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:26:37,649 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:26:37,649 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:26:37,681 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:26:37,682 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:26:37,682 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:26:37,682 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 08:26:37,682 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 08:26:41,466 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 08:26:52,636 - __main__ - GLOBAL - INFO - Received interrupt signal. Shutting down...
2025-05-28 08:26:52,639 - multi_tenant_manager - GLOBAL - INFO - Stopping all bot instances...
2025-05-28 08:26:52,639 - multi_tenant_manager - GLOBAL - INFO - Stopping bot for tenant: indx
2025-05-28 08:26:55,847 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 08:26:55,847 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 08:26:55,848 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 08:26:55,848 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 08:26:55,866 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 08:26:55,868 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:26:55,868 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 08:26:55,868 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 08:26:55,868 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 08:26:55,868 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:26:55,868 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:26:55,869 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:26:55,869 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:26:55,874 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 08:26:55,907 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 08:26:55,907 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 08:26:55,908 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:26:55,908 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:26:55,908 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:26:55,908 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:26:55,913 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 08:26:55,915 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 08:26:55,915 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 08:26:55,915 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:26:55,916 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:26:55,916 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:26:55,916 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 08:26:55,920 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 08:26:55,922 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 08:26:55,922 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 08:26:55,922 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 08:26:55,923 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 08:26:55,923 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:26:55,927 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 08:26:55,927 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:26:55,927 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:55,928 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:55,928 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:55,928 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:26:55,928 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 08:26:55,928 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:26:55,932 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 08:26:55,932 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:26:55,932 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:55,933 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:55,933 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:26:55,933 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:26:55,933 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 08:26:55,933 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 08:26:55,933 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 08:26:55,933 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 08:26:55,934 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 08:26:55,934 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 08:26:55,935 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 08:26:55,935 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 08:26:56,003 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 08:26:56,003 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 08:26:56,003 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:26:56,003 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:26:56,004 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:26:56,004 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 08:26:56,004 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 08:26:56,005 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:26:56,006 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 08:26:56,006 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 08:26:56,834 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:26:56,834 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:26:58,756 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 08:26:58,756 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:26:58,756 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 08:26:58,756 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:26:58,811 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:26:58,812 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:26:58,812 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:26:58,846 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:26:58,846 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:26:58,847 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:26:58,847 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 08:26:58,847 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 08:27:01,191 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 08:27:15,020 - __main__ - GLOBAL - INFO - Received interrupt signal. Shutting down...
2025-05-28 08:27:15,025 - multi_tenant_manager - GLOBAL - INFO - Stopping all bot instances...
2025-05-28 08:27:15,029 - multi_tenant_manager - GLOBAL - INFO - Stopping bot for tenant: indx
2025-05-28 08:27:19,979 - telegram_bot.indx - GLOBAL - INFO - Bot stopped by user
2025-05-28 08:27:19,984 - telegram_bot.indx - GLOBAL - INFO - Bot task was cancelled
2025-05-28 08:27:19,985 - telegram.ext.Application - GLOBAL - INFO - Application is stopping. This might take a moment.
2025-05-28 08:27:19,987 - telegram.ext.Application - GLOBAL - INFO - Application.stop() complete
2025-05-28 08:27:21,026 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Updater:start_polling:polling_task' coro=<network_retry_loop() running at /Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_utils/networkloop.py:115> wait_for=<Future finished result=None>>
2025-05-28 08:27:21,027 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-25' coro=<Event.wait() done, defined at /opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py:200> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-05-28 08:27:24,319 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 08:27:24,319 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 08:27:24,319 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 08:27:24,320 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 08:27:24,339 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 08:27:24,340 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:27:24,341 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 08:27:24,341 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 08:27:24,341 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 08:27:24,341 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:27:24,341 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:27:24,341 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:27:24,341 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:27:24,347 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 08:27:24,385 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 08:27:24,385 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 08:27:24,385 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:27:24,386 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:27:24,386 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:27:24,386 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:27:24,389 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 08:27:24,390 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 08:27:24,390 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 08:27:24,390 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:27:24,391 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:27:24,391 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:27:24,391 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 08:27:24,395 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 08:27:24,396 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 08:27:24,396 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 08:27:24,396 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 08:27:24,396 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 08:27:24,396 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:27:24,399 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 08:27:24,400 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:27:24,400 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:27:24,400 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:27:24,400 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:27:24,400 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:27:24,400 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 08:27:24,400 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:27:24,403 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 08:27:24,403 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:27:24,403 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:27:24,404 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:27:24,404 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:27:24,404 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:27:24,404 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 08:27:24,404 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 08:27:24,404 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 08:27:24,404 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 08:27:24,405 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 08:27:24,405 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 08:27:24,406 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 08:27:24,406 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 08:27:24,482 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 08:27:24,482 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 08:27:24,483 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:27:24,483 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:27:24,484 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:27:24,484 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:27:24,484 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 08:27:24,485 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 08:27:24,485 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 08:27:24,485 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 08:27:25,870 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:27:27,893 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 08:27:27,894 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:27:27,894 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 08:27:27,894 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:27:27,952 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:27:27,952 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:27:27,953 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:27:27,985 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:27:27,986 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:27:27,986 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:27:27,986 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 08:27:27,986 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 08:27:29,175 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 08:27:29,888 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:27:32,504 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-28 08:27:32,554 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-28 08:27:32,554 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-28 08:27:32,555 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:27:32,612 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:27:32,612 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:27:32,613 - telegram_bot.indx - GLOBAL - INFO - Loading YAML config for customer: indx
2025-05-28 08:27:32,648 - telegram_bot.indx - GLOBAL - ERROR - Invalid buttons structure in main_menu for customer: indx
2025-05-28 08:27:32,648 - telegram_bot.indx - GLOBAL - ERROR - Invalid configuration for customer: indx
2025-05-28 08:27:32,649 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-28 08:27:32,649 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-28 08:27:32,649 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 08:27:33,987 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 08:27:33,988 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-28 08:35:04,909 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-28 08:35:04,909 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-28 08:35:04,910 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-28 08:35:04,910 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-28 08:35:04,937 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-28 08:35:04,939 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:35:04,939 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-28 08:35:04,939 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-28 08:35:04,939 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-28 08:35:04,939 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-28 08:35:04,940 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:35:04,940 - telegram_bot.indx - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:35:04,940 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:35:04,946 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-28 08:35:04,966 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-28 08:35:04,966 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-28 08:35:04,967 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:35:04,967 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:35:04,967 - telegram_bot.newcustomer - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:35:04,967 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:35:04,970 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-28 08:35:04,971 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-28 08:35:04,972 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-28 08:35:04,972 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-28 08:35:04,972 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-28 08:35:04,973 - telegram_bot.rishux - GLOBAL - INFO - YAMLConfigManager initialized with base path: custom_menu
2025-05-28 08:35:04,973 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-28 08:35:04,976 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-28 08:35:04,977 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-28 08:35:04,977 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-28 08:35:04,977 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-28 08:35:04,977 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-28 08:35:04,977 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-28 08:35:04,981 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-28 08:35:04,981 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:35:04,982 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:35:04,982 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:35:04,982 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:35:04,982 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:35:04,982 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-28 08:35:04,982 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-28 08:35:04,985 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-28 08:35:04,985 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-28 08:35:04,985 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:35:04,985 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:35:04,985 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-28 08:35:04,985 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-28 08:35:04,986 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-28 08:35:04,986 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-28 08:35:04,986 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-28 08:35:04,986 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-28 08:35:04,987 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-28 08:35:04,987 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-28 08:35:04,987 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-28 08:35:04,989 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-28 08:35:05,067 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-28 08:35:05,068 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-28 08:35:05,068 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:35:05,068 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-28 08:35:05,069 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:35:05,069 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-28 08:35:05,069 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-28 08:35:05,070 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-28 08:35:05,071 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-28 08:35:05,071 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-28 08:35:06,328 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:35:06,336 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-28 08:35:11,861 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-28 08:35:11,866 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:35:11,866 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-28 08:35:11,866 - telegram_bot.indx - GLOBAL - ERROR - Error getting message text for menu main_menu: 'YAMLConfigManager' object has no attribute 'config_cache'
2025-05-28 08:35:11,866 - telegram_bot.indx - GLOBAL - ERROR - Error creating keyboard from YAML for menu main_menu: 'YAMLConfigManager' object has no attribute 'config_cache'
2025-05-28 08:35:11,867 - telegram_bot.indx - GLOBAL - WARNING - Failed to load main menu from YAML, using fallback
2025-05-28 08:35:11,867 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-28 08:35:11,868 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-28 08:35:11,868 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-28 08:35:13,191 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-28 08:36:16,028 - __main__ - GLOBAL - INFO - Received interrupt signal. Shutting down...
2025-05-28 08:36:16,031 - multi_tenant_manager - GLOBAL - INFO - Stopping all bot instances...
2025-05-28 08:36:16,031 - multi_tenant_manager - GLOBAL - INFO - Stopping bot for tenant: indx
2025-05-28 08:36:23,619 - telegram_bot.indx - GLOBAL - INFO - Bot stopped by user
2025-05-28 08:36:23,624 - telegram_bot.indx - GLOBAL - INFO - Bot task was cancelled
2025-05-28 08:36:23,626 - telegram.ext.Application - GLOBAL - INFO - Application is stopping. This might take a moment.
2025-05-28 08:36:23,628 - telegram.ext.Application - GLOBAL - INFO - Application.stop() complete
2025-05-28 08:36:24,764 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Updater:start_polling:polling_task' coro=<network_retry_loop() running at /Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_utils/networkloop.py:115> wait_for=<Future finished result=None>>
2025-05-28 08:36:24,764 - asyncio - GLOBAL - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-48' coro=<Event.wait() done, defined at /opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py:200> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-05-30 09:25:45,668 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 09:25:45,668 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 09:25:45,669 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 09:25:45,669 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 09:25:50,748 - tenant_manager - GLOBAL - WARNING - Failed to connect to MongoDB (attempt 1/5): mongodb:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68392c41396f71d884b161d8, topology_type: Unknown, servers: [<ServerDescription ('mongodb', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('mongodb:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-05-30 09:25:57,824 - tenant_manager - GLOBAL - WARNING - Failed to connect to MongoDB (attempt 2/5): mongodb:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68392c48396f71d884b161d9, topology_type: Unknown, servers: [<ServerDescription ('mongodb', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('mongodb:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-05-30 09:26:04,913 - tenant_manager - GLOBAL - WARNING - Failed to connect to MongoDB (attempt 3/5): mongodb:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 68392c4f396f71d884b161da, topology_type: Unknown, servers: [<ServerDescription ('mongodb', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('mongodb:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-05-30 09:26:09,966 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 09:26:09,969 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 09:26:09,969 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 09:26:09,969 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 09:26:09,969 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 09:26:09,969 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 09:26:09,971 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 09:26:09,972 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 09:26:09,983 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 09:26:10,095 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 09:26:10,096 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 09:26:10,096 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 09:26:10,097 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 09:26:10,097 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 09:26:10,100 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 09:26:10,103 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 09:26:10,104 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 09:26:10,104 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 09:26:10,105 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 09:26:10,105 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 09:26:10,108 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 09:26:10,110 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 09:26:10,111 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 09:26:10,111 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 09:26:10,111 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 09:26:10,111 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 09:26:10,115 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 09:26:10,115 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 09:26:10,116 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:26:10,116 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:26:10,116 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:26:10,116 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 09:26:10,116 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 09:26:10,116 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 09:26:10,122 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 09:26:10,122 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 09:26:10,123 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:26:10,123 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:26:10,123 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:26:10,123 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 09:26:10,123 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 09:26:10,123 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 09:26:10,123 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 09:26:10,124 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 09:26:10,129 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 09:26:10,138 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 09:26:10,139 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 09:26:10,139 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 09:26:10,229 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 09:26:10,229 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 09:26:10,230 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 09:26:10,230 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 09:26:10,230 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 09:26:10,231 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 09:26:10,231 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 09:26:10,231 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 09:26:10,231 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 09:26:10,231 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 09:26:11,576 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 09:26:11,866 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 09:26:13,144 - telegram_bot.indx - GLOBAL - ERROR - Update Update(callback_query=CallbackQuery(chat_instance='-5852857698608250218', data='new_registration', from_user=User(first_name='Lokendar', id=*********, is_bot=False, language_code='en'), id='2730888004705375654', message=Message(caption='Welcome to the verification Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈', channel_chat_created=False, chat=Chat(first_name='Lokendar', id=*********, type=<ChatType.PRIVATE>), date=datetime.datetime(2025, 5, 28, 3, 5, 12, tzinfo=datetime.timezone.utc), delete_chat_photo=False, from_user=User(first_name='MyAccessBot', id=**********, is_bot=True, username='MyAccessBot_1234_bot'), group_chat_created=False, message_id=4890, photo=(PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADcwADNgQ', file_size=832, file_unique_id='AQADnMUxG2PgqFV4', height=45, width=90), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADbQADNgQ', file_size=10492, file_unique_id='AQADnMUxG2PgqFVy', height=160, width=320), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADeAADNgQ', file_size=39168, file_unique_id='AQADnMUxG2PgqFV9', height=400, width=800), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADeQADNgQ', file_size=78433, file_unique_id='AQADnMUxG2PgqFV-', height=640, width=1280), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ', file_size=138262, file_unique_id='AQADnMUxG2PgqFV8', height=1024, width=2048)), reply_markup=InlineKeyboardMarkup(inline_keyboard=((InlineKeyboardButton(callback_data='new_registration', text='New Registration 🧑\u200d💻'),), (InlineKeyboardButton(callback_data='claim_membership', text='Claim Membership 🎖️'),), (InlineKeyboardButton(callback_data='verify_membership', text='Verify Membership 🔃'),), (InlineKeyboardButton(callback_data='wd_start', text='Withdrawals & Deposits 💱'),), (InlineKeyboardButton(callback_data='submit_request_start', text='Submit a Request 📪'),))), supergroup_chat_created=False)), update_id=840207456) caused error: Query is too old and response timeout expired or query id is invalid
Traceback (most recent call last):
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 116, in handle_callback
    await query.answer()
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_callbackquery.py", line 186, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_extbot.py", line 948, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 4182, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 691, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_extbot.py", line 362, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 720, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_baserequest.py", line 353, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-30 09:26:13,552 - telegram_bot.indx - GLOBAL - WARNING - User ********* clicking too fast: 0.79s since last click
2025-05-30 09:26:14,140 - telegram_bot.indx - GLOBAL - ERROR - Update Update(callback_query=CallbackQuery(chat_instance='-5852857698608250218', data='new_registration', from_user=User(first_name='Lokendar', id=*********, is_bot=False, language_code='en'), id='2730888001725027576', message=Message(caption='Welcome to the verification Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈', channel_chat_created=False, chat=Chat(first_name='Lokendar', id=*********, type=<ChatType.PRIVATE>), date=datetime.datetime(2025, 5, 28, 3, 5, 12, tzinfo=datetime.timezone.utc), delete_chat_photo=False, from_user=User(first_name='MyAccessBot', id=**********, is_bot=True, username='MyAccessBot_1234_bot'), group_chat_created=False, message_id=4890, photo=(PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADcwADNgQ', file_size=832, file_unique_id='AQADnMUxG2PgqFV4', height=45, width=90), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADbQADNgQ', file_size=10492, file_unique_id='AQADnMUxG2PgqFVy', height=160, width=320), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADeAADNgQ', file_size=39168, file_unique_id='AQADnMUxG2PgqFV9', height=400, width=800), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADeQADNgQ', file_size=78433, file_unique_id='AQADnMUxG2PgqFV-', height=640, width=1280), PhotoSize(file_id='AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ', file_size=138262, file_unique_id='AQADnMUxG2PgqFV8', height=1024, width=2048)), reply_markup=InlineKeyboardMarkup(inline_keyboard=((InlineKeyboardButton(callback_data='new_registration', text='New Registration 🧑\u200d💻'),), (InlineKeyboardButton(callback_data='claim_membership', text='Claim Membership 🎖️'),), (InlineKeyboardButton(callback_data='verify_membership', text='Verify Membership 🔃'),), (InlineKeyboardButton(callback_data='wd_start', text='Withdrawals & Deposits 💱'),), (InlineKeyboardButton(callback_data='submit_request_start', text='Submit a Request 📪'),))), supergroup_chat_created=False)), update_id=840207457) caused error: Query is too old and response timeout expired or query id is invalid
Traceback (most recent call last):
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 107, in handle_callback
    await query.answer("⏳ Please wait a moment before clicking again.", show_alert=True)
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_callbackquery.py", line 186, in answer
    return await self.get_bot().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_extbot.py", line 948, in answer_callback_query
    return await super().answer_callback_query(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 4182, in answer_callback_query
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 691, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_extbot.py", line 362, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/_bot.py", line 720, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/request/_baserequest.py", line 353, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-30 09:26:21,373 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-30 09:26:21,410 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 09:26:21,410 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 09:26:21,411 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 09:26:21,411 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 09:26:21,411 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 09:26:23,970 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 09:26:23,971 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-30 09:26:25,615 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-30 09:26:25,637 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 09:26:25,637 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: reg_want_to_join
2025-05-30 09:26:25,637 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 09:26:25,642 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 09:26:25,645 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 09:26:25,645 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 09:26:25,645 - telegram_bot.indx - GLOBAL - INFO - User data for *********: None
2025-05-30 09:26:25,646 - telegram_bot.indx - GLOBAL - INFO - No user data found for user *********
2025-05-30 09:26:25,646 - telegram_bot.indx - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 09:26:25,646 - telegram_bot.indx - GLOBAL - INFO - User ********* has status: new, proceeding with join steps
2025-05-30 09:26:25,649 - telegram_bot.indx - GLOBAL - INFO - Retrieved broker referral link from MongoDB: https://one.loki.org/a/ljekbvt0qm
2025-05-30 09:26:25,650 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 09:26:25,650 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 09:26:25,651 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 09:26:28,255 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 09:26:28,293 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-30 09:26:36,448 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 09:26:36,449 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 09:26:36,449 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 09:26:36,449 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 09:26:36,449 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 09:26:36,449 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 09:26:38,437 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 09:26:40,971 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-30 09:26:41,003 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 09:26:41,004 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-30 09:26:41,006 - telegram_bot.indx - GLOBAL - INFO - Retrieved broker referral link from MongoDB: https://one.loki.org/a/ljekbvt0qm
2025-05-30 09:26:41,006 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 09:26:41,007 - telegram_bot.indx - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 09:26:41,007 - telegram_bot.indx - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 09:26:41,007 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 09:26:41,007 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 09:26:46,217 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 09:26:46,218 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-30 09:26:49,668 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-30 09:26:49,690 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 09:26:49,691 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: account_created
2025-05-30 09:26:49,691 - telegram_bot.indx - GLOBAL - INFO - account_created callback for user *********
2025-05-30 09:26:49,691 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 09:26:49,694 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 09:26:49,697 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 09:26:49,697 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 09:26:49,697 - telegram_bot.indx - GLOBAL - INFO - User data for *********: None
2025-05-30 09:26:49,697 - telegram_bot.indx - GLOBAL - INFO - No user data found for user *********
2025-05-30 09:26:49,698 - telegram_bot.indx - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 09:26:49,698 - telegram_bot.indx - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-30 09:26:49,698 - telegram_bot.indx - GLOBAL - INFO - Account number image found at assets/account_number_image.png
2025-05-30 09:26:51,100 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-30 09:27:50,748 - telegram_bot.indx - GLOBAL - INFO - Received account number from user *********: *********
2025-05-30 09:27:50,751 - telegram_bot.indx - GLOBAL - INFO - Stored account number in context for user *********: *********
2025-05-30 09:27:50,762 - telegram_bot.indx - GLOBAL - INFO - Access code exists in access_code collection: True
2025-05-30 09:27:50,766 - telegram_bot.indx - GLOBAL - INFO - Found 0 active users with access code *********: []
2025-05-30 09:27:50,767 - telegram_bot.indx - GLOBAL - INFO - Account used by other check: [], existing users: []
2025-05-30 09:27:50,767 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 09:27:50,787 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 09:27:50,795 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 09:27:50,796 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 09:27:50,796 - telegram_bot.indx - GLOBAL - INFO - Is expired user check: None
2025-05-30 09:27:50,796 - telegram_bot.indx - GLOBAL - INFO - Account exists and is valid for user *********: True
2025-05-30 09:27:50,796 - telegram_bot.indx - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 09:27:50,804 - telegram_bot.indx - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 09:27:50,809 - telegram_bot.indx - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 09:27:50,809 - telegram_bot.indx - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 09:54:36,481 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 09:54:36,481 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 09:54:36,482 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 09:54:36,482 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 09:54:36,507 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 09:54:36,509 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 09:54:36,509 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 09:54:36,509 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 09:54:36,509 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 09:54:36,509 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 09:54:36,509 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 09:54:36,510 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 09:54:36,515 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 09:54:36,525 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 09:54:36,525 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 09:54:36,525 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 09:54:36,526 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 09:54:36,526 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 09:54:36,528 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 09:54:36,529 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 09:54:36,529 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 09:54:36,529 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 09:54:36,529 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 09:54:36,529 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 09:54:36,532 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 09:54:36,533 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 09:54:36,533 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 09:54:36,533 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 09:54:36,533 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 09:54:36,533 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 09:54:36,536 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 09:54:36,536 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 09:54:36,536 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:54:36,536 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:54:36,536 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:54:36,536 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 09:54:36,536 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 09:54:36,536 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 09:54:36,539 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 09:54:36,539 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 09:54:36,539 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:54:36,539 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:54:36,539 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 09:54:36,539 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 09:54:36,539 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 09:54:36,539 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 09:54:36,540 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 09:54:36,540 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 09:54:36,540 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 09:54:36,540 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 09:54:36,540 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 09:54:36,540 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 09:54:36,597 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 09:54:36,597 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 09:54:36,597 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 09:54:36,597 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 09:54:36,597 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 09:54:36,598 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 09:54:36,598 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 09:54:36,598 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 09:54:36,598 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 09:54:36,598 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 09:54:37,977 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 09:54:38,013 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 10:07:10,137 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 10:07:10,140 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 10:07:10,141 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 10:07:10,141 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 10:07:10,142 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 10:07:10,142 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 10:07:11,893 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:36:56,974 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 12:36:56,974 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 12:36:56,975 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 12:36:56,975 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 12:36:57,012 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 12:36:57,014 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:36:57,014 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 12:36:57,014 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 12:36:57,014 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 12:36:57,014 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:36:57,015 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:36:57,016 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:36:57,021 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 12:36:57,035 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 12:36:57,035 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 12:36:57,035 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:36:57,035 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:36:57,036 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:36:57,041 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 12:36:57,043 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 12:36:57,043 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 12:36:57,043 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:36:57,044 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:36:57,044 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 12:36:57,048 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 12:36:57,049 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 12:36:57,049 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 12:36:57,049 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 12:36:57,049 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 12:36:57,049 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:36:57,052 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 12:36:57,052 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:36:57,053 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 2 total entries
2025-05-30 12:36:57,053 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:36:57,053 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:36:57,053 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 2 total entries
2025-05-30 12:36:57,053 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:36:57,054 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:36:57,054 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 2 total entries
2025-05-30 12:36:57,054 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:36:57,054 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:36:57,054 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:36:57,054 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 12:36:57,054 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:36:57,057 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 12:36:57,057 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:36:57,057 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 2 total entries
2025-05-30 12:36:57,057 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 2 total entries
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 2 total entries
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:36:57,058 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:36:57,059 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 12:36:57,059 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 12:36:57,059 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 12:36:57,059 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 12:36:57,060 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 12:36:57,060 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 12:36:57,060 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 12:36:57,060 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 12:36:57,148 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 12:36:57,148 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 12:36:57,149 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:36:57,149 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:36:57,150 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:36:57,150 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 12:36:57,150 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 12:36:57,151 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:36:57,153 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 12:36:57,153 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 12:36:57,955 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:36:57,960 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:37:12,226 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:37:12,228 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:12,228 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:37:12,228 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:12,229 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:37:12,229 - telegram_bot.indx - GLOBAL - INFO - Attempting to send image using cached file_id: BAADEC51DDE5B01187667441
2025-05-30 12:37:12,864 - telegram_bot.indx - GLOBAL - WARNING - Cached file_id BAADEC51DDE5B01187667441 is invalid or expired: Wrong remote file identifier specified: can't unserialize it. wrong last symbol
2025-05-30 12:37:12,865 - telegram_bot.indx - GLOBAL - WARNING - Failed to send with cached file_id BAADEC51DDE5B01187667441, falling back to disk
2025-05-30 12:37:12,865 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:37:13,646 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:37:13,647 - telegram_bot.indx - GLOBAL - INFO - Cached file_id for indx/welcome (assets/welcome.webp): AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:22,669 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:37:22,670 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:22,670 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:37:22,671 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:22,671 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:37:22,671 - telegram_bot.indx - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:23,481 - telegram_bot.indx - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:23,482 - telegram_bot.indx - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:25,850 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:37:25,900 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:37:25,900 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 12:37:25,901 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:37:25,901 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:37:25,901 - telegram_bot.indx - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:37:26,925 - telegram_bot.indx - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:37:26,926 - telegram_bot.indx - GLOBAL - INFO - Cached file_id for indx/membership_offer (assets/membership_offer.webp): AgACAgUAAxkDAAISrGg1OD6RRQfVw4Lsp5JFc6usPJzBAAJfxzEbY-CoVVWGU2Zsfly4AQADAgADdwADNgQ
2025-05-30 12:37:26,926 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:37:35,384 - telegram_bot.indx - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:37:35,385 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:35,386 - telegram_bot.indx - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:37:35,386 - telegram_bot.indx - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:35,386 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:37:35,387 - telegram_bot.indx - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:36,230 - telegram_bot.indx - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:36,231 - telegram_bot.indx - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAISo2g1JXvnWNZpZQjp9pFXlQN2icVBAAKcxTEbY-CoVajoqDnair-TAQADAgADdwADNgQ
2025-05-30 12:37:38,017 - telegram_bot.indx - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:37:38,050 - telegram_bot.indx - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:37:38,050 - telegram_bot.indx - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 12:37:38,051 - telegram_bot.indx - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:37:38,051 - telegram_bot.indx - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:37:38,051 - telegram_bot.indx - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAISrGg1OD6RRQfVw4Lsp5JFc6usPJzBAAJfxzEbY-CoVVWGU2Zsfly4AQADAgADdwADNgQ
2025-05-30 12:37:38,431 - telegram_bot.indx - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAISrGg1OD6RRQfVw4Lsp5JFc6usPJzBAAJfxzEbY-CoVVWGU2Zsfly4AQADAgADdwADNgQ
2025-05-30 12:37:38,431 - telegram_bot.indx - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAISrGg1OD6RRQfVw4Lsp5JFc6usPJzBAAJfxzEbY-CoVVWGU2Zsfly4AQADAgADdwADNgQ
2025-05-30 12:37:38,432 - telegram_bot.indx - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:37:51,429 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:37:51,431 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:51,432 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:37:51,434 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:37:51,435 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:37:51,436 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:37:52,722 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:37:52,724 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/welcome (assets/welcome.webp): AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:37:56,735 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:37:56,754 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:37:56,756 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 12:37:56,756 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:37:56,756 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:37:56,757 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:37:57,832 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:37:57,834 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/membership_offer (assets/membership_offer.webp): AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:37:57,834 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:38:04,107 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:38:04,120 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:38:04,121 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-30 12:38:04,121 - telegram_bot.newcustomer - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-30 12:38:04,122 - telegram_bot.newcustomer - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-30 12:38:04,490 - telegram_bot.newcustomer - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-30 12:38:04,491 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:38:10,123 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:38:10,125 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:38:10,125 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:38:10,126 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:38:10,126 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:38:10,127 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:38:10,885 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:38:10,885 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:38:32,903 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:38:32,917 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:38:32,918 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-30 12:38:32,922 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 12:38:32,923 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:38:32,923 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 12:38:32,924 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:38:32,924 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 12:38:32,924 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:38:35,271 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:38:35,273 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/claim_membership (assets/claim_membership.webp): AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:38:35,274 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:38:39,676 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:38:39,686 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:38:39,687 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: account_created
2025-05-30 12:38:39,688 - telegram_bot.newcustomer - GLOBAL - INFO - account_created callback for user *********
2025-05-30 12:38:39,689 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:38:39,694 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:38:39,696 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:38:39,696 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:38:39,696 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:38:39,696 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:38:39,697 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:38:39,697 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-30 12:38:39,697 - telegram_bot.newcustomer - GLOBAL - INFO - Account number image found at assets/account_number_image.png
2025-05-30 12:38:41,567 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:38:59,546 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:38:59,547 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:38:59,548 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:38:59,548 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:38:59,549 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:38:59,549 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:00,500 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:00,500 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:08,655 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:39:08,667 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:39:08,668 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: submit_request_start
2025-05-30 12:39:08,668 - telegram_bot.newcustomer - GLOBAL - INFO - Starting submit_request_start for user *********
2025-05-30 12:39:08,668 - telegram_bot.newcustomer - GLOBAL - INFO - Cleared context.user_data for user *********
2025-05-30 12:39:08,669 - telegram_bot.newcustomer - GLOBAL - INFO - support image found at assets/support.webp
2025-05-30 12:39:08,669 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with support image
2025-05-30 12:39:08,669 - telegram_bot.newcustomer - GLOBAL - INFO - support image found at assets/support.webp
2025-05-30 12:39:08,670 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/support.webp
2025-05-30 12:39:08,670 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:39:09,698 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:39:09,700 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/support (assets/support.webp): AgACAgUAAxkDAANJaDZW5ISj8jEtHkM7LWlWpfsk7RYAAhbBMRt_BbhVitrsenI-p4UBAAMCAAN3AAM2BA
2025-05-30 12:39:10,175 - telegram_bot.newcustomer - GLOBAL - INFO - Returning state WAITING_FOR_REQUEST_TEXT for user *********
2025-05-30 12:39:10,175 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:39:20,834 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:39:20,836 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:39:20,836 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:39:20,837 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:39:20,837 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:39:20,837 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:21,654 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:21,655 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:32,382 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 12:39:32,382 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 12:39:32,382 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 12:39:32,383 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 12:39:32,406 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 12:39:32,408 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:39:32,408 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 12:39:32,409 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 12:39:32,409 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 12:39:32,409 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:39:32,409 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:39:32,410 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:39:32,417 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 12:39:32,437 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 12:39:32,437 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 12:39:32,437 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:39:32,438 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:39:32,438 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:39:32,444 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 12:39:32,446 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 12:39:32,446 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 12:39:32,446 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:39:32,447 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:39:32,447 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 12:39:32,450 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 12:39:32,451 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 12:39:32,452 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 12:39:32,452 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 12:39:32,452 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 12:39:32,452 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:39:32,455 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 12:39:32,455 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:39:32,455 - telegram_bot.indx - GLOBAL - ERROR - Failed to load cache file: Expecting value: line 1 column 1 (char 0)
2025-05-30 12:39:32,455 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - ERROR - Failed to load cache file: Expecting value: line 1 column 1 (char 0)
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - ERROR - Failed to load cache file: Expecting value: line 1 column 1 (char 0)
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:39:32,456 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:39:32,456 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 12:39:32,457 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:39:32,460 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 12:39:32,461 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:39:32,461 - telegram_bot.newcustomer - GLOBAL - ERROR - Failed to load cache file: Expecting value: line 1 column 1 (char 0)
2025-05-30 12:39:32,461 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:39:32,461 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:39:32,461 - telegram_bot.newcustomer - GLOBAL - ERROR - Failed to load cache file: Expecting value: line 1 column 1 (char 0)
2025-05-30 12:39:32,462 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:39:32,462 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:39:32,462 - telegram_bot.newcustomer - GLOBAL - ERROR - Failed to load cache file: Expecting value: line 1 column 1 (char 0)
2025-05-30 12:39:32,462 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:39:32,462 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:39:32,462 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:39:32,462 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 12:39:32,462 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 12:39:32,462 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 12:39:32,463 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 12:39:32,463 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 12:39:32,463 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 12:39:32,464 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 12:39:32,465 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 12:39:32,534 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 12:39:32,534 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 12:39:32,534 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:39:32,534 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:39:32,535 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:39:32,535 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 12:39:32,535 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 12:39:32,536 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:39:32,537 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 12:39:32,538 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 12:39:33,170 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:39:33,215 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:39:35,327 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:39:35,328 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:39:35,328 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:39:35,329 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:39:35,330 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:39:35,330 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:39:36,102 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:39:36,104 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/welcome (assets/welcome.webp): AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:39:43,638 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:39:43,651 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:39:43,652 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 12:39:43,652 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:39:43,653 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:39:43,653 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:39:44,692 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:39:44,693 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/membership_offer (assets/membership_offer.webp): AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:39:44,693 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:39:57,995 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:39:58,006 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:39:58,007 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-30 12:39:58,007 - telegram_bot.newcustomer - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-30 12:39:58,008 - telegram_bot.newcustomer - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-30 12:39:58,448 - telegram_bot.newcustomer - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-30 12:39:58,449 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:40:00,129 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:40:00,136 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:40:00,136 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-30 12:40:00,141 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 12:40:00,142 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:40:00,142 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 12:40:00,142 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:40:00,143 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 12:40:00,143 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:40:01,398 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:40:01,399 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/claim_membership (assets/claim_membership.webp): AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:01,400 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:40:05,701 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:40:05,714 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:40:05,715 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: claim_show_steps
2025-05-30 12:40:05,718 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 12:40:05,718 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:40:05,719 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 12:40:05,719 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:40:05,719 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 12:40:05,719 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:06,094 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:06,095 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:06,095 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:40:08,952 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:40:08,963 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:40:08,963 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: claim_membership
2025-05-30 12:40:08,965 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 12:40:08,965 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:40:08,965 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 12:40:08,966 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:40:08,966 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 12:40:08,966 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:09,450 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:09,451 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:40:09,451 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:40:18,049 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:40:18,059 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:40:18,060 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: account_created
2025-05-30 12:40:18,060 - telegram_bot.newcustomer - GLOBAL - INFO - account_created callback for user *********
2025-05-30 12:40:18,061 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:40:18,064 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:40:18,067 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:40:18,068 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:40:18,068 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:40:18,068 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:40:18,068 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:40:18,069 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-30 12:40:18,069 - telegram_bot.newcustomer - GLOBAL - INFO - Account number image found at assets/account_number_image.png
2025-05-30 12:40:18,878 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:40:25,996 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:40:26,010 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:40:26,011 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: submit_request_start
2025-05-30 12:40:26,011 - telegram_bot.newcustomer - GLOBAL - INFO - Starting submit_request_start for user *********
2025-05-30 12:40:26,011 - telegram_bot.newcustomer - GLOBAL - INFO - Cleared context.user_data for user *********
2025-05-30 12:40:26,012 - telegram_bot.newcustomer - GLOBAL - INFO - support image found at assets/support.webp
2025-05-30 12:40:26,012 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with support image
2025-05-30 12:40:26,012 - telegram_bot.newcustomer - GLOBAL - INFO - support image found at assets/support.webp
2025-05-30 12:40:26,012 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/support.webp
2025-05-30 12:40:26,013 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:40:27,374 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:40:27,375 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/support (assets/support.webp): AgACAgUAAxkDAANJaDZW5ISj8jEtHkM7LWlWpfsk7RYAAhbBMRt_BbhVitrsenI-p4UBAAMCAAN3AAM2BA
2025-05-30 12:40:27,745 - telegram_bot.newcustomer - GLOBAL - INFO - Returning state WAITING_FOR_REQUEST_TEXT for user *********
2025-05-30 12:40:27,745 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:40:31,770 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:40:31,777 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:40:31,777 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-30 12:40:31,778 - telegram_bot.newcustomer - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-30 12:40:31,779 - telegram_bot.newcustomer - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-30 12:40:32,184 - telegram_bot.newcustomer - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-30 12:40:32,185 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:50:15,067 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 12:50:15,068 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 12:50:15,068 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 12:50:15,068 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 12:50:15,093 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 12:50:15,095 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:50:15,095 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 12:50:15,095 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 12:50:15,095 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 12:50:15,095 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:50:15,095 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:50:15,096 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:50:15,101 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 12:50:15,116 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 12:50:15,117 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 12:50:15,117 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:50:15,117 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:50:15,117 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:50:15,120 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 12:50:15,121 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 12:50:15,121 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 12:50:15,121 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:50:15,122 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:50:15,122 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 12:50:15,126 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 12:50:15,127 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 12:50:15,127 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 12:50:15,127 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 12:50:15,127 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 12:50:15,127 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:50:15,130 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 12:50:15,130 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 4 total entries
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 4 total entries
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 4 total entries
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:50:15,131 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:50:15,132 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:50:15,132 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 12:50:15,132 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:50:15,134 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 12:50:15,134 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 4 total entries
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 4 total entries
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 4 total entries
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:50:15,135 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:50:15,136 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:50:15,136 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 12:50:15,136 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 12:50:15,136 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 12:50:15,136 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 12:50:15,137 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 12:50:15,137 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 12:50:15,138 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 12:50:15,139 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 12:50:15,211 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 12:50:15,211 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 12:50:15,211 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:50:15,211 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:50:15,212 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:50:15,213 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 12:50:15,213 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 12:50:15,213 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:50:15,215 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 12:50:15,215 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 12:50:18,163 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:50:18,466 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:50:25,172 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 12:50:25,173 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:50:25,174 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 12:50:25,175 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 12:50:25,175 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 12:50:25,175 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 12:50:26,841 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 12:50:26,843 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/welcome (assets/welcome.webp): AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 12:50:29,269 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:50:29,286 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:50:29,286 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 12:50:29,287 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:50:29,287 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:50:29,287 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:50:29,756 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:50:29,757 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:50:29,758 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:50:34,354 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:50:34,362 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:50:34,362 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_want_to_join
2025-05-30 12:50:34,363 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:50:34,366 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:50:34,369 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:50:34,369 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:50:34,370 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:50:34,370 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:50:34,370 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:50:34,370 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with join steps
2025-05-30 12:50:34,373 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 12:50:34,374 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:50:34,374 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:50:34,375 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:50:34,758 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:50:34,759 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:50:34,801 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:50:50,185 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:50:50,196 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:50:50,197 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_opened_account
2025-05-30 12:50:50,198 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:50:50,200 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:50:50,203 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:50:50,204 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:50:50,204 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:50:50,204 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:50:50,204 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:50:50,205 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-30 12:50:50,205 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:50:50,206 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 12:50:50,206 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:50:50,207 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 12:50:50,207 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:50:50,597 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:50:50,598 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:50:50,600 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:50:53,290 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:50:53,298 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:50:53,298 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_start_verification
2025-05-30 12:50:53,299 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:50:53,302 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:50:53,304 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:50:53,305 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:50:53,305 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:50:53,308 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:50:53,310 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:50:53,313 - telegram_bot.newcustomer - GLOBAL - INFO - Stored telegram_bot_id in context for user *********: **********
2025-05-30 12:50:53,313 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* doesn't have an active or pending subscription, proceeding with verification
2025-05-30 12:50:53,314 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:50:53,317 - telegram_bot.newcustomer - GLOBAL - ERROR - Update Update(callback_query=CallbackQuery(chat_instance='-1158143913955524105', data='reg_start_verification', from_user=User(first_name='Lokendar', id=*********, is_bot=False, language_code='en'), id='2730888001780539328', message=Message(caption='Well done! You have taken the first step to unlock endless possibilities in your trading career 📈\n\nHelp us with more info about yourself ℹ️', channel_chat_created=False, chat=Chat(first_name='Lokendar', id=*********, type=<ChatType.PRIVATE>), date=datetime.datetime(2025, 5, 30, 7, 20, 50, tzinfo=datetime.timezone.utc), delete_chat_photo=False, from_user=User(first_name='MyAccessBot_2', id=**********, is_bot=True, username='MyAccessBot_1234_2_bot'), group_chat_created=False, message_id=134, photo=(PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAANzAAM2BA', file_size=1331, file_unique_id='AQADDsExG38FuFV4', height=60, width=90), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAANtAAM2BA', file_size=17398, file_unique_id='AQADDsExG38FuFVy', height=213, width=320), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN4AAM2BA', file_size=68595, file_unique_id='AQADDsExG38FuFV9', height=533, width=800), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN5AAM2BA', file_size=138143, file_unique_id='AQADDsExG38FuFV-', height=853, width=1280), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA', file_size=170915, file_unique_id='AQADDsExG38FuFV8', height=1024, width=1536)), reply_markup=InlineKeyboardMarkup(inline_keyboard=((InlineKeyboardButton(callback_data='reg_start_verification', text='Start Verification ✅'),), (InlineKeyboardButton(callback_data='reg_want_to_join', text='Back to Previous Step'),), (InlineKeyboardButton(callback_data='new_registration', text='Back to Registration'),), (InlineKeyboardButton(callback_data='back_to_menu', text='Back to Main Menu'),))), supergroup_chat_created=False)), update_id=160030201) caused error: cannot access local variable 'ForceReply' where it is not associated with a value
Traceback (most recent call last):
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/conversationhandler.py", line 842, in handle_update
    new_state: object = await handler.handle_update(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 131, in handle_callback
    result = await self.message_helpers.with_user_processing_lock(user_id, process_callback)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/utils/message_helpers.py", line 82, in with_user_processing_lock
    result = await operation()
             ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 129, in process_callback
    return await self._handle_callback_internal(update, context, query, callback_data, user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 858, in _handle_callback_internal
    ForceReply(selective=True),
    ^^^^^^^^^^
UnboundLocalError: cannot access local variable 'ForceReply' where it is not associated with a value
2025-05-30 12:50:57,358 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:50:57,369 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:50:57,370 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: back_to_menu
2025-05-30 12:50:57,371 - telegram_bot.newcustomer - GLOBAL - INFO - Going back to menu for user *********, editing current message
2025-05-30 12:50:57,372 - telegram_bot.newcustomer - GLOBAL - INFO - Editing caption for message with photo (attempt 1)
2025-05-30 12:50:57,757 - telegram_bot.newcustomer - GLOBAL - INFO - Message edited successfully (attempt 1)
2025-05-30 12:50:57,758 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:50:59,716 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:50:59,722 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:50:59,722 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 12:50:59,723 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:50:59,724 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:50:59,724 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:51:00,162 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:51:00,163 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:51:00,164 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:51:02,366 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:51:02,375 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:51:02,375 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_want_to_join
2025-05-30 12:51:02,376 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:51:02,378 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:51:02,381 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:51:02,382 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:51:02,382 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:51:02,383 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:51:02,383 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:51:02,383 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with join steps
2025-05-30 12:51:02,387 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 12:51:02,388 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 12:51:02,388 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 12:51:02,388 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:51:02,935 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:51:02,935 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 12:51:02,935 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:51:04,747 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:51:04,753 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:51:04,753 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_opened_account
2025-05-30 12:51:04,753 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:51:04,757 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:51:04,760 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:51:04,761 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:51:04,761 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:51:04,761 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:51:04,761 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:51:04,762 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-30 12:51:04,762 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:51:04,762 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 12:51:04,762 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 12:51:04,763 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 12:51:04,763 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:51:05,226 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:51:05,227 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 12:51:05,227 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:51:08,737 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:51:08,743 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:51:08,744 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_start_verification
2025-05-30 12:51:08,744 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:51:08,746 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:51:08,748 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:51:08,749 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:51:08,749 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:51:08,750 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:51:08,751 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:51:08,751 - telegram_bot.newcustomer - GLOBAL - INFO - Stored telegram_bot_id in context for user *********: **********
2025-05-30 12:51:08,752 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* doesn't have an active or pending subscription, proceeding with verification
2025-05-30 12:51:08,752 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:51:08,753 - telegram_bot.newcustomer - GLOBAL - ERROR - Update Update(callback_query=CallbackQuery(chat_instance='-1158143913955524105', data='reg_start_verification', from_user=User(first_name='Lokendar', id=*********, is_bot=False, language_code='en'), id='2730888003010752033', message=Message(caption='Well done! You have taken the first step to unlock endless possibilities in your trading career 📈\n\nHelp us with more info about yourself ℹ️', channel_chat_created=False, chat=Chat(first_name='Lokendar', id=*********, type=<ChatType.PRIVATE>), date=datetime.datetime(2025, 5, 30, 7, 21, 5, tzinfo=datetime.timezone.utc), delete_chat_photo=False, from_user=User(first_name='MyAccessBot_2', id=**********, is_bot=True, username='MyAccessBot_1234_2_bot'), group_chat_created=False, message_id=138, photo=(PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAANzAAM2BA', file_size=1331, file_unique_id='AQADDsExG38FuFV4', height=60, width=90), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAANtAAM2BA', file_size=17398, file_unique_id='AQADDsExG38FuFVy', height=213, width=320), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN4AAM2BA', file_size=68595, file_unique_id='AQADDsExG38FuFV9', height=533, width=800), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN5AAM2BA', file_size=138143, file_unique_id='AQADDsExG38FuFV-', height=853, width=1280), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA', file_size=170915, file_unique_id='AQADDsExG38FuFV8', height=1024, width=1536)), reply_markup=InlineKeyboardMarkup(inline_keyboard=((InlineKeyboardButton(callback_data='reg_start_verification', text='Start Verification ✅'),), (InlineKeyboardButton(callback_data='reg_want_to_join', text='Back to Previous Step'),), (InlineKeyboardButton(callback_data='new_registration', text='Back to Registration'),), (InlineKeyboardButton(callback_data='back_to_menu', text='Back to Main Menu'),))), supergroup_chat_created=False)), update_id=160030206) caused error: cannot access local variable 'ForceReply' where it is not associated with a value
Traceback (most recent call last):
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/conversationhandler.py", line 842, in handle_update
    new_state: object = await handler.handle_update(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 131, in handle_callback
    result = await self.message_helpers.with_user_processing_lock(user_id, process_callback)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/utils/message_helpers.py", line 82, in with_user_processing_lock
    result = await operation()
             ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 129, in process_callback
    return await self._handle_callback_internal(update, context, query, callback_data, user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 858, in _handle_callback_internal
    ForceReply(selective=True),
    ^^^^^^^^^^
UnboundLocalError: cannot access local variable 'ForceReply' where it is not associated with a value
2025-05-30 12:51:40,520 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 12:51:40,522 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 12:51:40,523 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 12:51:40,523 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 12:51:40,558 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 12:51:40,560 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:51:40,560 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 12:51:40,560 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 12:51:40,560 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 12:51:40,560 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 12:51:40,561 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:51:40,561 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:51:40,566 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 12:51:40,580 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 12:51:40,581 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 12:51:40,581 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:51:40,581 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:51:40,581 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:51:40,584 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 12:51:40,585 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 12:51:40,585 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 12:51:40,585 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 12:51:40,585 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 12:51:40,585 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 12:51:40,589 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 12:51:40,591 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 12:51:40,591 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 12:51:40,591 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 12:51:40,591 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 12:51:40,591 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 12:51:40,594 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 12:51:40,594 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:51:40,595 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:51:40,596 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 12:51:40,596 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 12:51:40,599 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 12:51:40,599 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 12:51:40,599 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 12:51:40,599 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:51:40,599 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 12:51:40,600 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 12:51:40,600 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 12:51:40,600 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 12:51:40,600 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 12:51:40,601 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 12:51:40,601 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 12:51:40,601 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 12:51:40,602 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 12:51:40,602 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 12:51:40,673 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 12:51:40,673 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 12:51:40,673 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:51:40,673 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 12:51:40,674 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:51:40,675 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 12:51:40,675 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 12:51:40,675 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 12:51:40,677 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 12:51:40,677 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 12:51:41,483 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:51:41,516 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 12:51:45,826 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 12:51:45,836 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 12:51:45,836 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_start_verification
2025-05-30 12:51:45,837 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 12:51:45,840 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 12:51:45,843 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 12:51:45,843 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 12:51:45,843 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 12:51:45,843 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 12:51:45,844 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 12:51:45,844 - telegram_bot.newcustomer - GLOBAL - INFO - Stored telegram_bot_id in context for user *********: **********
2025-05-30 12:51:45,845 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* doesn't have an active or pending subscription, proceeding with verification
2025-05-30 12:51:45,846 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 12:51:45,848 - telegram_bot.newcustomer - GLOBAL - ERROR - Update Update(callback_query=CallbackQuery(chat_instance='-1158143913955524105', data='reg_start_verification', from_user=User(first_name='Lokendar', id=*********, is_bot=False, language_code='en'), id='2730888001449535383', message=Message(caption='Well done! You have taken the first step to unlock endless possibilities in your trading career 📈\n\nHelp us with more info about yourself ℹ️', channel_chat_created=False, chat=Chat(first_name='Lokendar', id=*********, type=<ChatType.PRIVATE>), date=datetime.datetime(2025, 5, 30, 7, 21, 5, tzinfo=datetime.timezone.utc), delete_chat_photo=False, from_user=User(first_name='MyAccessBot_2', id=**********, is_bot=True, username='MyAccessBot_1234_2_bot'), group_chat_created=False, message_id=138, photo=(PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAANzAAM2BA', file_size=1331, file_unique_id='AQADDsExG38FuFV4', height=60, width=90), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAANtAAM2BA', file_size=17398, file_unique_id='AQADDsExG38FuFVy', height=213, width=320), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN4AAM2BA', file_size=68595, file_unique_id='AQADDsExG38FuFV9', height=533, width=800), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN5AAM2BA', file_size=138143, file_unique_id='AQADDsExG38FuFV-', height=853, width=1280), PhotoSize(file_id='AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA', file_size=170915, file_unique_id='AQADDsExG38FuFV8', height=1024, width=1536)), reply_markup=InlineKeyboardMarkup(inline_keyboard=((InlineKeyboardButton(callback_data='reg_start_verification', text='Start Verification ✅'),), (InlineKeyboardButton(callback_data='reg_want_to_join', text='Back to Previous Step'),), (InlineKeyboardButton(callback_data='new_registration', text='Back to Registration'),), (InlineKeyboardButton(callback_data='back_to_menu', text='Back to Main Menu'),))), supergroup_chat_created=False)), update_id=160030207) caused error: cannot access local variable 'ForceReply' where it is not associated with a value
Traceback (most recent call last):
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_application.py", line 1298, in process_update
    await coroutine
  File "/Users/<USER>/Documents/aws/project/telegram_bot/venv/lib/python3.11/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 131, in handle_callback
    result = await self.message_helpers.with_user_processing_lock(user_id, process_callback)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/utils/message_helpers.py", line 82, in with_user_processing_lock
    result = await operation()
             ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 129, in process_callback
    return await self._handle_callback_internal(update, context, query, callback_data, user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/aws/project/telegram_bot/src/handlers/callback_handlers.py", line 858, in _handle_callback_internal
    ForceReply(selective=True),
    ^^^^^^^^^^
UnboundLocalError: cannot access local variable 'ForceReply' where it is not associated with a value
2025-05-30 13:02:58,379 - __main__ - GLOBAL - INFO - Starting Telegram Bot application in multi-tenant mode
2025-05-30 13:02:58,379 - __main__ - GLOBAL - INFO - Initializing Multi-Tenant Bot Manager
2025-05-30 13:02:58,380 - multi_tenant_manager - GLOBAL - INFO - Using MongoDB URI from config.json: mongodb://mongodb:27017/
2025-05-30 13:02:58,380 - multi_tenant_manager - GLOBAL - INFO - Discovering tenant databases...
2025-05-30 13:02:58,417 - tenant_manager - GLOBAL - INFO - Successfully connected to MongoDB server
2025-05-30 13:02:58,420 - tenant_manager - GLOBAL - INFO - Discovered 3 tenant databases: indx, newcustomer, rishux
2025-05-30 13:02:58,420 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: indx_custdb
2025-05-30 13:02:58,420 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: newcustomer_custdb
2025-05-30 13:02:58,420 - tenant_manager - GLOBAL - INFO - Created connection to tenant database: rishux_custdb
2025-05-30 13:02:58,420 - multi_tenant_manager - GLOBAL - INFO - Found 3 tenant databases: indx, newcustomer, rishux
2025-05-30 13:02:58,421 - telegram_bot.indx - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 13:02:58,421 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 13:02:58,427 - telegram_bot.indx - GLOBAL - INFO - Loading bot configuration from tenant database: indx_custdb
2025-05-30 13:02:58,441 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant indx for bot: MyAccessBot
2025-05-30 13:02:58,441 - telegram_bot.indx - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant indx: -1002682842201
2025-05-30 13:02:58,441 - telegram_bot.indx - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 13:02:58,442 - telegram_bot.newcustomer - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 13:02:58,442 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 13:02:58,447 - telegram_bot.newcustomer - GLOBAL - INFO - Loading bot configuration from tenant database: newcustomer_custdb
2025-05-30 13:02:58,448 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded bot token from MongoDB for tenant newcustomer for bot: MyAccessBot_2
2025-05-30 13:02:58,448 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully loaded channel ID from MongoDB for tenant newcustomer: -1002682842201
2025-05-30 13:02:58,448 - telegram_bot.newcustomer - GLOBAL - INFO - Updated sensitive data filter with token from MongoDB
2025-05-30 13:02:58,449 - telegram_bot.rishux - GLOBAL - INFO - Logging initialized with level: INFO
2025-05-30 13:02:58,449 - database.rishux - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: rishux
2025-05-30 13:02:58,452 - telegram_bot.rishux - GLOBAL - INFO - Loading bot configuration from tenant database: rishux_custdb
2025-05-30 13:02:58,453 - telegram_bot.rishux - GLOBAL - ERROR - ERROR: No bot information found in MongoDB telegram_bots collection for tenant rishux
2025-05-30 13:02:58,453 - multi_tenant_manager - GLOBAL - INFO - Created configs for 2 tenants
2025-05-30 13:02:58,453 - __main__ - GLOBAL - INFO - Initializing bots for all tenants
2025-05-30 13:02:58,453 - multi_tenant_manager - GLOBAL - INFO - Initializing bot instances for all tenants...
2025-05-30 13:02:58,453 - database.indx - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: indx
2025-05-30 13:02:58,456 - telegram_bot.indx - GLOBAL - INFO - SubscriptionManager initialized for tenant: indx
2025-05-30 13:02:58,456 - telegram_bot.indx - GLOBAL - INFO - Initializing bot handlers
2025-05-30 13:02:58,457 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 13:02:58,457 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 13:02:58,457 - telegram_bot.indx - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 13:02:58,457 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 13:02:58,457 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 13:02:58,458 - telegram_bot.indx - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 13:02:58,458 - telegram_bot.indx - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 13:02:58,458 - telegram_bot.indx - GLOBAL - INFO - ImageManager initialized for tenant: indx
2025-05-30 13:02:58,458 - telegram_bot.indx - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 13:02:58,458 - telegram_bot.indx - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 13:02:58,458 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: indx
2025-05-30 13:02:58,458 - database.newcustomer - GLOBAL - INFO - Initializing database in multi-tenant mode for tenant: newcustomer
2025-05-30 13:02:58,462 - telegram_bot.newcustomer - GLOBAL - INFO - SubscriptionManager initialized for tenant: newcustomer
2025-05-30 13:02:58,462 - telegram_bot.newcustomer - GLOBAL - INFO - Initializing bot handlers
2025-05-30 13:02:58,462 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 13:02:58,462 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 13:02:58,462 - telegram_bot.newcustomer - GLOBAL - INFO - CommandHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - MessageHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - Loaded image cache with 2 tenants and 5 total entries
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - ImageManager initialized for tenant: newcustomer
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed
2025-05-30 13:02:58,463 - telegram_bot.newcustomer - GLOBAL - INFO - Bot handlers initialized successfully
2025-05-30 13:02:58,463 - multi_tenant_manager - GLOBAL - INFO - Initialized bot for tenant: newcustomer
2025-05-30 13:02:58,463 - __main__ - GLOBAL - INFO - Starting all bots
2025-05-30 13:02:58,464 - multi_tenant_manager - GLOBAL - INFO - Starting all bot instances...
2025-05-30 13:02:58,464 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: indx
2025-05-30 13:02:58,465 - multi_tenant_manager - GLOBAL - INFO - Started bot for tenant: newcustomer
2025-05-30 13:02:58,465 - __main__ - GLOBAL - INFO - All bots started. Press Ctrl+C to exit.
2025-05-30 13:02:58,465 - telegram_bot.indx - GLOBAL - INFO - Creating application with token: 75984...
2025-05-30 13:02:58,465 - telegram_bot.newcustomer - GLOBAL - INFO - Creating application with token: 81562...
2025-05-30 13:02:58,546 - telegram_bot.newcustomer - GLOBAL - INFO - Setting tenant name in bot data: newcustomer
2025-05-30 13:02:58,546 - telegram_bot.indx - GLOBAL - INFO - Setting tenant name in bot data: indx
2025-05-30 13:02:58,546 - telegram_bot.indx - GLOBAL - INFO - Setting up conversation handler
2025-05-30 13:02:58,546 - telegram_bot.newcustomer - GLOBAL - INFO - Setting up conversation handler
2025-05-30 13:02:58,548 - telegram_bot.newcustomer - GLOBAL - INFO - Adding handlers to application
2025-05-30 13:02:58,548 - telegram_bot.newcustomer - GLOBAL - INFO - Adding help command
2025-05-30 13:02:58,548 - telegram_bot.newcustomer - GLOBAL - INFO - Starting polling
2025-05-30 13:02:58,550 - telegram_bot.indx - GLOBAL - INFO - Adding handlers to application
2025-05-30 13:02:58,550 - telegram_bot.indx - GLOBAL - INFO - Adding help command
2025-05-30 13:02:58,551 - telegram_bot.indx - GLOBAL - INFO - Starting polling
2025-05-30 13:02:59,463 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 13:02:59,467 - telegram.ext.Application - GLOBAL - INFO - Application started
2025-05-30 13:03:02,564 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 13:03:02,574 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 13:03:02,574 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_start_verification
2025-05-30 13:03:02,574 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 13:03:02,576 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 13:03:02,579 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 13:03:02,579 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 13:03:02,579 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 13:03:02,579 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 13:03:02,579 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 13:03:02,580 - telegram_bot.newcustomer - GLOBAL - INFO - Stored telegram_bot_id in context for user *********: **********
2025-05-30 13:03:02,580 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* doesn't have an active or pending subscription, proceeding with verification
2025-05-30 13:03:02,581 - telegram_bot.newcustomer - GLOBAL - INFO - account_number image found at assets/account_number_image.png
2025-05-30 13:03:02,581 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/account_number_image.png
2025-05-30 13:03:02,581 - telegram_bot.newcustomer - GLOBAL - INFO - File opened successfully, sending photo (attempt 1)...
2025-05-30 13:03:03,823 - telegram_bot.newcustomer - GLOBAL - INFO - Photo sent successfully (attempt 1)
2025-05-30 13:03:03,824 - telegram_bot.newcustomer - GLOBAL - INFO - Cached file_id for newcustomer/account_number (assets/account_number_image.png): AgACAgUAAxkDAAMiaDZLeOVOLnId3sahcqmzyEUki-IAAg_BMRt_BbhV1Uqd2haWClsBAAMCAAN5AAM2BA
2025-05-30 13:03:03,825 - telegram_bot.newcustomer - GLOBAL - INFO - Setting conversation state to WAITING_FOR_ACCOUNT_NUMBER for user *********
2025-05-30 13:03:03,825 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 13:04:03,064 - telegram_bot.newcustomer - GLOBAL - INFO - Start command received from user ********* (@Unknown)
2025-05-30 13:04:03,066 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 13:04:03,066 - telegram_bot.newcustomer - GLOBAL - INFO - Showing subscription menu to user *********
2025-05-30 13:04:03,067 - telegram_bot.newcustomer - GLOBAL - INFO - welcome image found at assets/welcome.webp
2025-05-30 13:04:03,067 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/welcome.webp
2025-05-30 13:04:03,067 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 13:04:03,887 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 13:04:03,887 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMVaDUsCXC4eEZgEMWfqYkzrTLhzjEAAmzEMRt_BahVFtCmjgWAm5QBAAMCAAN3AAM2BA
2025-05-30 13:04:06,020 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 13:04:06,033 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 13:04:06,033 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: new_registration
2025-05-30 13:04:06,034 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 13:04:06,034 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 13:04:06,034 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 13:04:06,444 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 13:04:06,444 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 13:04:06,445 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 13:04:07,790 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 13:04:07,798 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 13:04:07,798 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_want_to_join
2025-05-30 13:04:07,799 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 13:04:07,802 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 13:04:07,804 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 13:04:07,805 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 13:04:07,805 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 13:04:07,805 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 13:04:07,806 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 13:04:07,806 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with join steps
2025-05-30 13:04:07,808 - telegram_bot.newcustomer - GLOBAL - WARNING - Broker referral link not found in MongoDB bot data, using default
2025-05-30 13:04:07,809 - telegram_bot.newcustomer - GLOBAL - INFO - membership_offer image found at assets/membership_offer.webp
2025-05-30 13:04:07,810 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/membership_offer.webp
2025-05-30 13:04:07,811 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 13:04:08,216 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 13:04:08,217 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMWaDU3sYVIxlGs4RxcFu0Qyp6A0rkAAibTMRt_BbBVDqrso8QJbjgBAAMCAAN3AAM2BA
2025-05-30 13:04:08,253 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 13:04:09,843 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 13:04:09,851 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 13:04:09,851 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_opened_account
2025-05-30 13:04:09,852 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 13:04:09,855 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 13:04:09,857 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 13:04:09,858 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 13:04:09,858 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 13:04:09,858 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 13:04:09,858 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 13:04:09,859 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* has status: new, proceeding with verification
2025-05-30 13:04:09,860 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 13:04:09,860 - telegram_bot.newcustomer - GLOBAL - INFO - Replacing message with claim_membership image
2025-05-30 13:04:09,861 - telegram_bot.newcustomer - GLOBAL - INFO - claim_membership image found at assets/claim_membership.webp
2025-05-30 13:04:09,861 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/claim_membership.webp
2025-05-30 13:04:09,861 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 13:04:10,302 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 13:04:10,302 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMgaDZK2E5NENaNC-lE24CvqEY4fj4AAg7BMRt_BbhVVT5Bzr8mIo8BAAMCAAN3AAM2BA
2025-05-30 13:04:10,303 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 13:04:13,917 - telegram_bot.newcustomer - GLOBAL - INFO - Started processing for user *********
2025-05-30 13:04:13,924 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* interaction saved for bot **********
2025-05-30 13:04:13,925 - telegram_bot.newcustomer - GLOBAL - INFO - Callback from user *********: reg_start_verification
2025-05-30 13:04:13,926 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 13:04:13,929 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 13:04:13,932 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 13:04:13,932 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 13:04:13,933 - telegram_bot.newcustomer - GLOBAL - INFO - User data for *********: None
2025-05-30 13:04:13,933 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user *********
2025-05-30 13:04:13,933 - telegram_bot.newcustomer - GLOBAL - INFO - Subscription check result for user *********: is_verified=False, status=new, status_message=You don't have an active subscription. Please register first.
2025-05-30 13:04:13,933 - telegram_bot.newcustomer - GLOBAL - INFO - Stored telegram_bot_id in context for user *********: **********
2025-05-30 13:04:13,933 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* doesn't have an active or pending subscription, proceeding with verification
2025-05-30 13:04:13,934 - telegram_bot.newcustomer - GLOBAL - INFO - account_number image found at assets/account_number_image.png
2025-05-30 13:04:13,934 - telegram_bot.newcustomer - GLOBAL - INFO - Sending message with image: assets/account_number_image.png
2025-05-30 13:04:13,934 - telegram_bot.newcustomer - GLOBAL - INFO - Attempting to send image using cached file_id: AgACAgUAAxkDAAMiaDZLeOVOLnId3sahcqmzyEUki-IAAg_BMRt_BbhV1Uqd2haWClsBAAMCAAN5AAM2BA
2025-05-30 13:04:14,328 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMiaDZLeOVOLnId3sahcqmzyEUki-IAAg_BMRt_BbhV1Uqd2haWClsBAAMCAAN5AAM2BA
2025-05-30 13:04:14,329 - telegram_bot.newcustomer - GLOBAL - INFO - Successfully sent image using cached file_id: AgACAgUAAxkDAAMiaDZLeOVOLnId3sahcqmzyEUki-IAAg_BMRt_BbhV1Uqd2haWClsBAAMCAAN5AAM2BA
2025-05-30 13:04:14,329 - telegram_bot.newcustomer - GLOBAL - INFO - Setting conversation state to WAITING_FOR_ACCOUNT_NUMBER for user *********
2025-05-30 13:04:14,329 - telegram_bot.newcustomer - GLOBAL - INFO - Finished processing for user *********
2025-05-30 13:04:17,503 - telegram_bot.newcustomer - GLOBAL - INFO - Received account number from user *********: *********
2025-05-30 13:04:17,504 - telegram_bot.newcustomer - GLOBAL - INFO - Stored account number in context for user *********: *********
2025-05-30 13:04:17,507 - telegram_bot.newcustomer - GLOBAL - INFO - Access code exists in access_code collection: True
2025-05-30 13:04:17,512 - telegram_bot.newcustomer - GLOBAL - INFO - Found 0 active users with access code *********: []
2025-05-30 13:04:17,512 - telegram_bot.newcustomer - GLOBAL - INFO - Account used by other check: [], existing users: []
2025-05-30 13:04:17,512 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 13:04:17,514 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 13:04:17,515 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 13:04:17,515 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 13:04:17,516 - telegram_bot.newcustomer - GLOBAL - INFO - Is expired user check: None
2025-05-30 13:04:17,516 - telegram_bot.newcustomer - GLOBAL - INFO - Account exists and is valid for user *********: True
2025-05-30 13:04:17,516 - telegram_bot.newcustomer - GLOBAL - INFO - Account is valid, checking if user ********* exists in master_user_data
2025-05-30 13:04:17,516 - telegram_bot.newcustomer - GLOBAL - INFO - Querying master_user_data for user_id: *********
2025-05-30 13:04:17,518 - telegram_bot.newcustomer - GLOBAL - INFO - Trying to find user with user_id as str: *********
2025-05-30 13:04:17,521 - telegram_bot.newcustomer - GLOBAL - INFO - Query result for user_id *********: None
2025-05-30 13:04:17,521 - telegram_bot.newcustomer - GLOBAL - INFO - No user data found for user_id *********
2025-05-30 13:04:17,521 - telegram_bot.newcustomer - GLOBAL - INFO - Master user data for user *********: None
2025-05-30 13:04:17,521 - telegram_bot.newcustomer - GLOBAL - INFO - User ********* doesn't exist in master_user_data or has incomplete data, asking for name
2025-05-30 13:04:17,930 - telegram_bot.newcustomer - GLOBAL - INFO - Asked user ********* for their name, returning WAITING_FOR_NAME state
