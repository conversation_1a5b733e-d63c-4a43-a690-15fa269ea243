from datetime import datetime
import csv
import os
import io

class MasterUserData:
    """
    Manages the master user data collection which stores user information
    in a flat structure for easy export to CSV.
    """
    
    def __init__(self, db):
        """
        Initialize the MasterUserData manager.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self._ensure_collection()
    
    def _ensure_collection(self):
        """
        Ensure the master_user_data collection exists with proper indexes.
        """
        if 'master_user_data' not in self.db.list_collection_names():
            self.db.create_collection('master_user_data')
            # Create unique index on user_id field
            self.db.master_user_data.create_index('user_id', unique=True)
    
    def sync_from_subscriptions(self):
        """
        Sync data from subscriptions collection to master_user_data.
        Returns the number of records synced.
        """
        count = 0
        subscriptions = self.db.subscriptions.find({})
        
        for sub in subscriptions:
            user_id = sub.get('user_id')
            if not user_id:
                continue
                
            # Extract user details
            user_details = sub.get('user_details', {})
            
            # Create flat user data structure
            user_data = {
                'user_id': user_id,
                'access_code': sub.get('access_code', ''),
                'subscription_time': sub.get('subscription_time', datetime.utcnow()),
                'status': sub.get('status', 'unknown'),
                'name': user_details.get('name', ''),
                'email': user_details.get('email', ''),
                'whatsapp': user_details.get('whatsapp', ''),
                'trading_experience': user_details.get('trading_experience', ''),
                'last_updated': datetime.utcnow()
            }
            
            # Update or insert into master_user_data
            self.db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': user_data},
                upsert=True
            )
            count += 1
            
        return count
    
    def add_or_update_user(self, user_id, user_data):
        """
        Add or update a user in the master_user_data collection.
        
        Args:
            user_id: The user's Telegram ID
            user_data: Dictionary containing user data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure user_id is included and is an integer
            user_data['user_id'] = int(user_id)
            user_data['last_updated'] = datetime.utcnow()
            
            self.db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': user_data},
                upsert=True
            )
            return True
        except Exception as e:
            print(f"Error updating master user data: {e}")
            return False
    
    def get_all_users(self):
        """
        Get all users from the master_user_data collection.
        
        Returns:
            List of user data dictionaries
        """
        users = list(self.db.master_user_data.find({}))
        
        # Convert ObjectId to string for serialization
        for user in users:
            if '_id' in user:
                user['_id'] = str(user['_id'])
                
        return users
    
    def get_user(self, user_id):
        """
        Get a specific user from the master_user_data collection.
        
        Args:
            user_id: The user's Telegram ID
            
        Returns:
            User data dictionary or None if not found
        """
        user = self.db.master_user_data.find_one({'user_id': user_id})
        
        if user and '_id' in user:
            user['_id'] = str(user['_id'])
            
        return user
    
    def export_to_csv(self, file_path=None):
        """
        Export all user data to CSV format.
        
        Args:
            file_path: Optional path to save the CSV file
            
        Returns:
            If file_path is provided: True if successful, False otherwise
            If file_path is not provided: CSV content as string
        """
        try:
            users = self.get_all_users()
            
            if not users:
                return False if file_path else ""
            
            # Get all possible fields from all users
            fields = set()
            for user in users:
                fields.update(user.keys())
            
            # Sort fields for consistent output
            fields = sorted(list(fields))
            
            # Create CSV
            if file_path:
                with open(file_path, 'w', newline='') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fields)
                    writer.writeheader()
                    writer.writerows(users)
                return True
            else:
                output = io.StringIO()
                writer = csv.DictWriter(output, fieldnames=fields)
                writer.writeheader()
                writer.writerows(users)
                return output.getvalue()
                
        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False if file_path else ""
