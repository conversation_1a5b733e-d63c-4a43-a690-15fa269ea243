# Account Number Image for Telegram Bot

This directory should contain an image file named `account_number_image.png` that shows users where to find their account number in the trading profile.

## Usage

1. Place your account number image in this directory with the name `account_number_image.png`
2. The image will be automatically sent to users when they click the "Start Verification ✅" button

## Image Requirements

- The image should be in PNG format
- Name the file `account_number_image.png`
- Keep the file size reasonable (under 5MB) for faster loading
- The image should clearly show where users can find their account number in the trading platform

## How It Works

When a user clicks the "Start Verification ✅" button:

1. The bot checks if `assets/account_number_image.png` exists
2. If the image exists, the bot sends the image with a caption asking for the account number
3. If the image doesn't exist, the bot falls back to sending just the text message asking for the account number

## Troubleshooting

If the account number image is not being sent:

1. Make sure the image file exists at `assets/account_number_image.png`
2. Check the bot logs for any errors related to sending the image
3. Verify that the image file is not corrupted and is in a valid PNG format
4. Ensure the file size is not too large (Telegram has a 10MB limit for photos)

If there are any issues with sending the image, the bot will automatically fall back to sending just the text message.
