# Welcome Image for Telegram Bot

This directory contains the welcome image that is sent to users when they start the bot with the `/start` command.

## Usage

1. Place your welcome image in this directory with the name `welcome.webp`
2. The image will be automatically sent to users when they start the bot with the welcome message and menu buttons in a single message

## Image Requirements

- The image should be in WebP format
- Name the file `welcome.webp`
- Keep the file size reasonable (under 5MB) for faster loading
- Recommended dimensions: 1280x720 pixels or similar aspect ratio

## How It Works

When a user sends the `/start` command:

1. The bot checks if `telegram_bot/assets/welcome.webp` exists
2. If the image exists, the bot sends the welcome message with the image and menu buttons in a single message
3. If the image doesn't exist, the bot falls back to sending just the text message with menu buttons

The welcome message sent with the image is:

```
Welcome to the verification Junction ✅

Unlock the potential of expert-led Forex trading world 📈
```

This is the same text that appears in the menu for non-subscribed users.

## Troubleshooting

If the welcome image is not being sent:

1. Make sure the image file exists at `telegram_bot/assets/welcome.webp`
2. Check the bot logs for any errors related to sending the image
3. Verify that the image file is not corrupted and is in a valid WebP format
4. Ensure the file size is not too large (Telegram has a 10MB limit for photos)

If there are any issues with sending the image, the bot will automatically fall back to sending just the text message.
