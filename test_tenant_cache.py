#!/usr/bin/env python3
"""
Test script to verify tenant-specific image caching functionality.
"""

import os
import json
from src.utils.image_manager import ImageManager

def simulate_telegram_response(image_path, tenant_name):
    """Simulate a Telegram response with a tenant-specific file_id"""
    import hashlib
    # Create tenant-specific file_id
    unique_string = f"{tenant_name}_{image_path}"
    fake_file_id = "BAAD" + hashlib.md5(unique_string.encode()).hexdigest()[:20].upper()
    
    class FakePhoto:
        def __init__(self, file_id):
            self.file_id = file_id
    
    class FakeMessage:
        def __init__(self, file_id):
            self.photo = [FakePhoto(file_id)]
    
    return FakeMessage(fake_file_id)

def test_tenant_specific_caching():
    """Test that caching is tenant-specific"""
    print("🏢 Testing Tenant-Specific Image Caching")
    print("=" * 60)
    
    # Clear any existing cache for clean test
    cache_file = "cache/cache.json"
    if os.path.exists(cache_file):
        os.remove(cache_file)
        print("🧹 Cleared existing cache for clean test")
    
    # Test tenants
    tenants = ["indx", "rishux", "newcustomer"]
    image_type = "welcome"
    
    # Create ImageManager instances for each tenant
    tenant_managers = {}
    for tenant in tenants:
        tenant_managers[tenant] = ImageManager(tenant_name=tenant)
        print(f"📋 Created ImageManager for tenant: {tenant}")
    
    print(f"\n📸 Testing image type: {image_type}")
    print("=" * 60)
    
    # Round 1: Each tenant caches their own file_id
    print("\n🔄 ROUND 1: Each tenant caches their own file_id")
    print("-" * 40)
    
    for tenant in tenants:
        manager = tenant_managers[tenant]
        image_path = manager.get_image_path(image_type)
        
        if image_path:
            print(f"\n🏢 Tenant: {tenant}")
            print(f"  📁 Image path: {image_path}")
            
            # Check cache (should be empty)
            cached_file_id = manager._get_cached_file_id(image_path)
            if cached_file_id:
                print(f"  ✅ Cache HIT: {cached_file_id}")
            else:
                print(f"  ❌ Cache MISS: No cached file_id")
                
                # Simulate sending and getting tenant-specific file_id
                fake_response = simulate_telegram_response(image_path, tenant)
                file_id = fake_response.photo[-1].file_id
                
                # Cache the file_id
                manager._cache_file_id(image_type, image_path, file_id)
                print(f"  💾 Cached file_id: {file_id}")
    
    # Show cache structure
    print(f"\n📋 Cache structure after Round 1:")
    if os.path.exists(cache_file):
        with open(cache_file, 'r') as f:
            cache_content = json.load(f)
            print(json.dumps(cache_content, indent=2))
    
    # Round 2: Verify each tenant gets their own cached file_id
    print("\n" + "=" * 60)
    print("🔄 ROUND 2: Verify tenant-specific cache retrieval")
    print("-" * 40)
    
    for tenant in tenants:
        manager = tenant_managers[tenant]
        image_path = manager.get_image_path(image_type)
        
        if image_path:
            print(f"\n🏢 Tenant: {tenant}")
            cached_file_id = manager._get_cached_file_id(image_path)
            if cached_file_id:
                print(f"  ✅ Cache HIT: {cached_file_id}")
                print(f"  ⚡ Using tenant-specific cached file_id")
            else:
                print(f"  ❌ Cache MISS: This shouldn't happen!")
    
    # Round 3: Test cache isolation (one tenant's cache doesn't affect another)
    print("\n" + "=" * 60)
    print("🔄 ROUND 3: Test cache isolation between tenants")
    print("-" * 40)
    
    # Update cache for one tenant
    test_tenant = tenants[0]
    manager = tenant_managers[test_tenant]
    image_path = manager.get_image_path(image_type)
    
    print(f"\n🔄 Updating cache for tenant: {test_tenant}")
    new_fake_response = simulate_telegram_response(image_path + "_updated", test_tenant)
    new_file_id = new_fake_response.photo[-1].file_id
    manager._cache_file_id(image_type, image_path, new_file_id)
    print(f"  💾 Updated file_id: {new_file_id}")
    
    # Verify other tenants are unaffected
    print(f"\n🔍 Checking other tenants are unaffected:")
    for tenant in tenants:
        manager = tenant_managers[tenant]
        image_path = manager.get_image_path(image_type)
        cached_file_id = manager._get_cached_file_id(image_path)
        
        print(f"  🏢 {tenant}: {cached_file_id}")
        if tenant == test_tenant:
            print(f"    ✅ Updated as expected")
        else:
            print(f"    ✅ Unchanged (isolated)")
    
    # Final cache structure
    print(f"\n📋 Final cache structure:")
    if os.path.exists(cache_file):
        with open(cache_file, 'r') as f:
            cache_content = json.load(f)
            print(json.dumps(cache_content, indent=2))
    
    print("\n" + "=" * 60)
    print("✅ Tenant-specific caching test completed!")
    print("\n🎯 Key Features Verified:")
    print("  • Each tenant has separate cache namespace")
    print("  • File IDs are tenant-specific (bot-specific)")
    print("  • Cache isolation between tenants")
    print("  • Shared cache file with tenant separation")

def test_cache_structure():
    """Test the expected cache structure"""
    print("\n📋 Testing Expected Cache Structure")
    print("=" * 60)
    
    expected_structure = {
        "tenant1": {
            "image_type1": {
                "image_path1": "file_id1",
                "image_path2": "file_id2"
            },
            "image_type2": {
                "image_path3": "file_id3"
            }
        },
        "tenant2": {
            "image_type1": {
                "image_path1": "file_id4"  # Different file_id for same path
            }
        }
    }
    
    print("Expected structure:")
    print(json.dumps(expected_structure, indent=2))
    
    print("\n✅ This structure ensures:")
    print("  • Tenant isolation: Each tenant has their own namespace")
    print("  • Bot-specific file_ids: Same image, different bots = different file_ids")
    print("  • Efficient storage: Single file with organized structure")
    print("  • Easy maintenance: Clear tenant separation")

if __name__ == "__main__":
    test_tenant_specific_caching()
    test_cache_structure()
