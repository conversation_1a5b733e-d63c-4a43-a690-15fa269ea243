import sys
import asyncio
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ConversationHandler, CallbackQueryHandler

from src.handlers.command_handlers import CommandHandlers, WAITING_FOR_CODE, HELP_ROUTE
from src.handlers.message_handlers import (
    MessageHand<PERSON>, WAITING_FOR_EMAIL, WAITING_FOR_WHATSAPP, WAITING_FOR_EXPERIENCE,
    WAITING_FOR_ACCOUNT_NUMBER, WAITING_FOR_NAME, WAITING_FOR_VERIFICATION_EMAIL, WAITING_FOR_VERIFICATION_WHATSAPP,
    WAITING_FOR_VERIFY_ACCOUNT_NUMBER, WAITING_FOR_REQUEST_TEXT, WAITING_FOR_REQUEST_NAME, WAITING_FOR_REQUEST_EMAIL,
    WAITING_FOR_REQUEST_WHATSAPP, WAITING_FOR_REQUEST_EXPERIENCE, WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT, WAITING_FOR_VERIFICATION_REQUEST_NAME,
    WAITING_FOR_VERIFICATION_REQUEST_EMAIL, WAITING_FOR_VERIFICATION_REQUEST_WHATSAPP, WAITING_FOR_VERIFICATION_REQUEST_EXPERIENCE
)
from src.handlers.callback_handlers import CallbackHandlers

class TelegramBot:
    """Main Telegram bot class"""

    def __init__(self, config, subscription_manager):
        self.config = config
        self.logger = config.get_logger()
        self.sub_manager = subscription_manager
        self.token = config.get('bot_token')
        self.tenant_name = config.get_tenant_name()  # Get tenant name from config

        self.logger.info("Initializing bot handlers")
        # Initialize handlers
        self.command_handlers = CommandHandlers(config, subscription_manager)
        self.message_handlers = MessageHandlers(config, subscription_manager)
        self.callback_handlers = CallbackHandlers(config, subscription_manager, self.command_handlers)
        self.logger.info("Bot handlers initialized successfully")

    async def error_handler(self, update, context):
        """Handle errors"""
        self.logger.error(f"Update {update} caused error: {context.error}", exc_info=context.error)
        if update and update.effective_message:
            await update.effective_message.reply_text("An error occurred while processing your request.")

    async def run_async(self):
        """Run the bot asynchronously"""
        # Check if we have a valid token
        if not self.token or self.token == "YOUR_BOT_TOKEN_HERE":
            self.logger.error("No valid bot token found. Cannot start the bot.")
            print("ERROR: No valid bot token found. Cannot start the bot.")
            sys.exit(1)

        # Create application
        self.logger.info(f"Creating application with token: {self.token[:5]}...")
        application = Application.builder().token(self.token).build()

        # Store tenant name in application's bot data
        if self.tenant_name:
            self.logger.info(f"Setting tenant name in bot data: {self.tenant_name}")
            application.bot_data['tenant_name'] = self.tenant_name

        # Create conversation handler
        self.logger.info("Setting up conversation handler")
        conv_handler = ConversationHandler(
            entry_points=[CommandHandler("start", self.command_handlers.start)],
            states={
                WAITING_FOR_CODE: [
                    CommandHandler("start", self.command_handlers.start),  # Add this line
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.check_code),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                WAITING_FOR_EMAIL: [
                    CommandHandler("start", self.command_handlers.start),  # Add this line
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_email)
                ],
                WAITING_FOR_WHATSAPP: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_whatsapp)
                ],
                WAITING_FOR_EXPERIENCE: [
                    CommandHandler("start", self.command_handlers.start),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                # New states for account verification workflow
                WAITING_FOR_ACCOUNT_NUMBER: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.check_account_number),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                WAITING_FOR_NAME: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_name)
                ],
                WAITING_FOR_VERIFICATION_EMAIL: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_email)
                ],
                WAITING_FOR_VERIFICATION_WHATSAPP: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_whatsapp)
                ],
                WAITING_FOR_VERIFY_ACCOUNT_NUMBER: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.check_verify_account_number),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                WAITING_FOR_REQUEST_TEXT: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_request_text),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                WAITING_FOR_REQUEST_NAME: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_request_name)
                ],
                WAITING_FOR_REQUEST_EMAIL: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_request_email)
                ],
                WAITING_FOR_REQUEST_WHATSAPP: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_request_whatsapp)
                ],
                WAITING_FOR_REQUEST_EXPERIENCE: [
                    CommandHandler("start", self.command_handlers.start),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_request_account)
                ],
                WAITING_FOR_VERIFICATION_REQUEST_NAME: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_request_name)
                ],
                WAITING_FOR_VERIFICATION_REQUEST_EMAIL: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_request_email)
                ],
                WAITING_FOR_VERIFICATION_REQUEST_WHATSAPP: [
                    CommandHandler("start", self.command_handlers.start),
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handlers.handle_verification_request_whatsapp)
                ],
                WAITING_FOR_VERIFICATION_REQUEST_EXPERIENCE: [
                    CommandHandler("start", self.command_handlers.start),
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ],
                HELP_ROUTE: [
                    CallbackQueryHandler(self.callback_handlers.handle_callback)
                ]
            },
            fallbacks=[CommandHandler("cancel", self.command_handlers.cancel)],
            per_chat=True,
            per_user=True
        )

        # Add global callback handler for non-conversation callbacks
        callback_handler = CallbackQueryHandler(self.callback_handlers.handle_callback)

        # Add handlers
        self.logger.info("Adding handlers to application")
        application.add_error_handler(self.error_handler)
        application.add_handler(conv_handler)
        application.add_handler(callback_handler)

        # Admin commands are handled by the web service

        # Add help command
        self.logger.info("Adding help command")
        application.add_handler(CommandHandler("help", self.command_handlers.help))

        # Start polling
        self.logger.info("Starting polling")
        await application.initialize()
        await application.start()
        await application.updater.start_polling()

        # Keep the application running
        try:
            # Keep the application running until stopped
            # Use an event to wait indefinitely
            stop_event = asyncio.Event()
            await stop_event.wait()
        except asyncio.CancelledError:
            self.logger.info("Bot task was cancelled")
        finally:
            # Properly shut down when stopped
            await application.stop()

    def run(self):
        """Run the bot (synchronous wrapper for run_async)"""
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Create a task for the async method
        task = None

        try:
            # Create and run the task
            task = loop.create_task(self.run_async())

            # Run the event loop until the task is done
            loop.run_forever()
        except KeyboardInterrupt:
            # Handle Ctrl+C gracefully
            self.logger.info("Bot stopped by user")
        except Exception as e:
            # Log any other exceptions
            self.logger.error(f"Error running bot: {e}", exc_info=True)
        finally:
            # Cancel the task if it's still running
            if task and not task.done():
                task.cancel()

                # Run the event loop until the task is cancelled
                try:
                    loop.run_until_complete(task)
                except asyncio.CancelledError:
                    pass

            # Stop the event loop
            loop.stop()

            # Close the event loop
            loop.close()