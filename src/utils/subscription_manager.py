from datetime import datetime, timezone

class SubscriptionManager:
    """Manages user subscriptions and access codes"""

    def __init__(self, config, db):
        self.config = config
        self.logger = config.get_logger()
        self.db = db
        self.tenant_name = config.get_tenant_name()  # Get tenant name from config
        self.logger.info(f"SubscriptionManager initialized for tenant: {self.tenant_name}")

    def get_bot_id_safely(self, update_or_query):
        """Get the bot ID safely from an update or query object

        Args:
            update_or_query: Either a telegram.Update or telegram.CallbackQuery object

        Returns:
            int or None: The bot ID if found, None otherwise
        """
        try:
            # Check if it's a callback query
            if hasattr(update_or_query, 'message') and update_or_query.message and hasattr(update_or_query.message, 'bot'):
                return update_or_query.message.bot.id
            # Check if it's an update with a message
            elif hasattr(update_or_query, 'effective_message') and update_or_query.effective_message and hasattr(update_or_query.effective_message, 'bot'):
                return update_or_query.effective_message.bot.id
            # Check if it has a bot attribute directly
            elif hasattr(update_or_query, 'bot') and update_or_query.bot:
                return update_or_query.bot.id
            # Check if it has a _bot attribute (sometimes used in callback queries)
            elif hasattr(update_or_query, '_bot') and update_or_query._bot:
                return update_or_query._bot.id
            else:
                self.logger.warning("Could not get bot ID from update or query object")
                return None
        except Exception as e:
            self.logger.error(f"Error getting bot ID: {str(e)}")
            return None

    def get_tenant_name_from_context(self, context):
        """Get the tenant name from the context

        Args:
            context: The context object from the update

        Returns:
            str or None: The tenant name if found, None otherwise
        """
        try:
            # Try to get tenant name from bot_data
            if hasattr(context, 'bot_data') and 'tenant_name' in context.bot_data:
                tenant_name = context.bot_data['tenant_name']
                self.logger.info(f"Found tenant name in context: {tenant_name}")
                return tenant_name
            else:
                self.logger.warning("Could not find tenant name in context")
                # Fall back to the tenant name from config
                return self.tenant_name
        except Exception as e:
            self.logger.error(f"Error getting tenant name from context: {str(e)}")
            # Fall back to the tenant name from config
            return self.tenant_name

    def get_tenant_database(self, context=None):
        """Get the database for the current tenant

        Args:
            context: Optional context object to get tenant name from

        Returns:
            Database: The database for the current tenant
        """
        try:
            # Get tenant name from context if provided
            tenant_name = self.get_tenant_name_from_context(context) if context else self.tenant_name

            # If tenant name is different from the current one, get a new database connection
            if tenant_name and tenant_name != self.tenant_name:
                self.logger.info(f"Switching to tenant database: {tenant_name}")
                from db.database import Database
                return Database(tenant_name=tenant_name)
            else:
                # Use the current database
                return self.db
        except Exception as e:
            self.logger.error(f"Error getting tenant database: {str(e)}")
            # Fall back to the current database
            return self.db

    def check_subscription(self, user_id):
        """Check if a user has an active subscription by checking master_user_data"""
        try:
            # Get user data from master_user_data collection
            user_data = self.get_master_user_data(user_id)
            # Check if user exists and has active subscription status
            return user_data is not None and user_data.get('subscription') == 'active'
        except Exception as e:
            self.logger.error(f"Error checking subscription for user {user_id}: {str(e)}")
            return False

    def get_subscription_status(self, user_id):
        """Get the subscription status for a user"""
        try:
            # Get user data from master_user_data collection
            user_data = self.get_master_user_data(user_id)
            # Return the subscription status if user exists, otherwise return None
            return user_data.get('subscription') if user_data else None
        except Exception as e:
            self.logger.error(f"Error getting subscription status for user {user_id}: {str(e)}")
            return None

    def check_subscription_status_and_get_message(self, user_id):
        """Check subscription status and return appropriate message

        Returns:
            tuple: (is_active, status, message)
                is_active (bool): True if subscription is active, False otherwise
                status (str): 'active', 'pending', 'inactive', or 'new'
                message (str): Message to display to the user
        """
        try:
            # Get user data from master_user_data collection
            user_data = self.get_master_user_data(user_id)
            self.logger.info(f"User data for {user_id}: {user_data}")

            if not user_data:
                self.logger.info(f"No user data found for user {user_id}")
                return False, 'new', "You don't have an active subscription. Please register first."

            subscription_status = user_data.get('subscription', 'unknown')
            user_verify = user_data.get('user_verify', False)
            broker_reg = user_data.get('broker_reg', False)
            user_status = user_data.get('user_status', 'unknown')
            self.logger.info(f"Subscription status for user {user_id}: {subscription_status}, user_verify: {user_verify}, broker_reg: {broker_reg}, user_status: {user_status}")

            # Check if user is verified
            if subscription_status == 'active' and user_verify:
                self.logger.info(f"User {user_id} has active subscription and is verified")
                return True, 'active', None  # Active subscription, no message needed
            elif user_status == 'verification_denied':
                self.logger.info(f"User {user_id} has verification denied status")
                return False, 'denied', "Your verification request has been denied. Please click the button below to provide your access code again."
            elif subscription_status == 'inactive' and not user_verify:
                self.logger.info(f"User {user_id} has inactive subscription and is not verified")
                return False, 'inactive', "Your account is not yet verified. Our team will verify your account soon, you will be notified once it's activated."
            elif subscription_status == 'pending':
                self.logger.info(f"User {user_id} has pending subscription")
                return False, 'pending', "Your account is currently under verification. Our team will notify you once the verification is complete."
            elif subscription_status == 'inactive':
                self.logger.info(f"User {user_id} has inactive subscription")
                return False, 'inactive', "Your subscription is inactive. Please contact support to reactivate it."
            else:
                self.logger.info(f"User {user_id} has unknown subscription status: {subscription_status}")
                return False, 'unknown', "Your subscription status is unknown. Please contact support for assistance."
        except Exception as e:
            self.logger.error(f"Error checking subscription status for user {user_id}: {str(e)}")
            return False, 'error', "There was an error checking your subscription status. Please try again later."

    def add_subscription(self, user_id, access_code, subscription_data=None):
        """Add a subscription by saving to master_user_data"""
        try:
            # Check if code is valid and not used by another user
            is_valid = self.verify_access_code(access_code, user_id)
            if not is_valid:
                return False, "This access code is not valid"

            # Create master user data
            master_user_data = {
                'user_id': user_id,
                'access_code': access_code,
                'subscription': 'inactive',  # Default to inactive until verified
                'user_verify': False,  # Default to not verified
                'broker_reg': True,    # User has a valid access code, so they are registered with broker
                'user_status': 'pending_verification',  # Pending verification since broker_reg=True but user_verify=False
                'user_verify_status': 'pending',
                'notification_history': {},  # Initialize notification_history
                'registration_time': datetime.now(timezone.utc)
            }

            # Add telegram_bot_id if provided in subscription_data
            if subscription_data and 'telegram_bot_id' in subscription_data:
                master_user_data['telegram_bot_id'] = subscription_data['telegram_bot_id']

            # Add user details if provided
            if subscription_data and 'user_details' in subscription_data:
                user_details = subscription_data['user_details']
                master_user_data.update({
                    'name': user_details.get('name', 'Unknown'),
                    'email': user_details.get('email', 'Unknown'),
                    'whatsapp': user_details.get('whatsapp', 'Unknown'),
                    'trading_experience': user_details.get('trading_experience', 'Unknown')
                })

            # Save to master_user_data collection
            success, auto_verified = self.save_to_master_user_data(master_user_data)

            if success:
                self.logger.info(f"Added subscription for {user_id} with code {access_code} to master_user_data")
                if auto_verified:
                    return True, "Access granted successfully! Your account has been automatically verified."
                else:
                    return True, "Access granted successfully! Your account is pending verification."
            return False, "Failed to save subscription"

        except Exception as e:
            self.logger.error(f"Subscription error for {user_id}: {str(e)}")
            return False, "An error occurred while processing your request"

    def get_users_by_code(self, access_code, context=None):
        """Get all users using a specific access code from master_user_data"""
        try:
            # Get the appropriate database for the current tenant
            db = self.get_tenant_database(context)

            # Query the master_user_data collection for the access code
            # Use a fresh query to get the most up-to-date data to prevent race conditions
            users = list(db.db.master_user_data.find({"access_code": str(access_code)}))

            # Filter out users with expired subscriptions
            active_users = []
            for user in users:
                user_id = user.get("user_id")
                user_status = user.get("user_status")
                subscription = user.get("subscription")

                # Only include users with active subscriptions
                if user_status != "expired" and subscription != "expired":
                    active_users.append(user_id)

            self.logger.info(f"Found {len(active_users)} active users with access code {access_code}: {active_users}")
            return active_users
        except Exception as e:
            self.logger.error(f"Error getting users for code {access_code}: {str(e)}")
            return []

    def get_access_codes(self):
        """Get all available access codes"""
        try:
            return self.db.get_access_codes()
        except Exception as e:
            self.logger.error(f"Error getting access codes: {str(e)}")
            return []

    def get_user_data(self, user_id):
        """Get user data from master_user_data collection"""
        try:
            return self.get_master_user_data(user_id)
        except Exception as e:
            self.logger.error(f"Error getting data for user {user_id}: {str(e)}")
            return None

    def verify_access_code(self, code: str, user_id=None, context=None) -> bool:
        """Check if an access code is valid and not already used by another user

        Args:
            code: The access code to verify
            user_id: Optional user ID to exclude from the check (for the current user)
            context: Optional context object to get tenant name from

        Returns:
            bool: True if the code is valid and not used by another user, False otherwise
        """
        try:
            # Get the appropriate database for the current tenant
            db = self.get_tenant_database(context)

            # First check if the code exists in the access_code collection
            is_valid_in_access_codes = db.verify_access_code(code)

            # If the code doesn't exist in access_code collection, it's not valid
            if not is_valid_in_access_codes:
                self.logger.warning(f"Access code {code} not found in access_code collection")
                return False

            # Get the most up-to-date list of users with this code
            # This is critical for preventing race conditions
            existing_users = self.get_users_by_code(code, context)

            # If there are existing users with this code
            if existing_users:
                # Get all user documents with this access code to check their status
                user_docs = list(db.db.master_user_data.find({"access_code": code}))

                # If user_id is provided, check if the code is used by another user
                if user_id is not None:
                    # Convert all user IDs to strings for comparison
                    existing_user_ids = [str(u) for u in existing_users]
                    current_user_id = str(user_id)

                    # If the code is used by another user (not the current user), it's not valid
                    # Even if the code exists in access_code collection
                    if current_user_id not in existing_user_ids:
                        self.logger.warning(f"Access code {code} is already used by another user: {existing_user_ids}")
                        return False

                    # If the code is used by the current user, check if their subscription is expired
                    for user_doc in user_docs:
                        if str(user_doc.get('user_id')) == current_user_id:
                            # If the user's status is expired, the code is not valid
                            if user_doc.get('user_status') == 'expired' or user_doc.get('subscription') == 'expired':
                                self.logger.warning(f"Access code {code} belongs to user {user_id} but their subscription is expired")
                                return False

                    # If we get here, the code is valid for this user
                    return True
                # If no user_id is provided but the code is already used, it's not valid
                else:
                    # Check if any of the users with this code have an active subscription
                    for user_doc in user_docs:
                        # If any user has an active subscription with this code, it's not valid for a new user
                        if user_doc.get('user_status') != 'expired' and user_doc.get('subscription') != 'expired':
                            self.logger.warning(f"Access code {code} is already used by an active user: {user_doc.get('user_id')}")
                            return False

                    # If all users with this code have expired subscriptions, check if it's valid in access_codes
                    if is_valid_in_access_codes:
                        return True
                    else:
                        self.logger.warning(f"Access code {code} is associated with expired users and not found in access_codes")
                        return False

            # If the code is not used by any user and exists in access_code collection, it's valid
            return True
        except Exception as e:
            self.logger.error(f"Error verifying access code {code}: {str(e)}")
            # In case of error, assume the code is not valid to be safe
            return False

    def save_telegram_bot_data(self, user_id, data):
        """Save data to telegram_bots collection by delegating to the database"""
        # Pass the call to the database instance
        return self.db.save_telegram_bot_data(user_id, data)

    def update_bot_user_interaction(self, bot_id, user_id, interaction_data):
        """Update a specific bot's user interaction data"""
        try:
            # Find the bot document by telegram_id
            bot_doc = self.db.db.telegram_bots.find_one({"telegram_id": bot_id})

            if not bot_doc:
                return False, f"Bot with ID {bot_id} not found"

            # If the bot doesn't have a users field yet, initialize it
            update_query = {
                "$set": {
                    f"users.{user_id}": interaction_data,
                    "last_updated": datetime.now(timezone.utc)
                }
            }

            # Update the specific bot document
            result = self.db.db.telegram_bots.update_one(
                {"telegram_id": bot_id},
                update_query
            )

            if result.modified_count > 0 or result.matched_count > 0:
                return True, "Bot user interaction updated successfully"
            else:
                return False, "Failed to update bot user interaction"
        except Exception as e:
            return False, f"Error updating bot user interaction: {str(e)}"

    # Master User Data methods
    def sync_master_user_data(self):
        """This method is kept for backward compatibility but no longer syncs from subscriptions"""
        try:
            self.logger.info("sync_master_user_data is deprecated as we now use only master_user_data collection")
            return True, "No sync needed as we now use only master_user_data collection"
        except Exception as e:
            error_msg = f"Error in sync_master_user_data: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_all_master_users(self):
        """Get all users from master_user_data collection"""
        try:
            return self.db.get_all_master_users()
        except Exception as e:
            self.logger.error(f"Error getting master users: {str(e)}")
            return []

    def get_master_user(self, user_id):
        """Get a specific user from master_user_data collection"""
        try:
            return self.db.get_master_user(user_id)
        except Exception as e:
            self.logger.error(f"Error getting master user {user_id}: {str(e)}")
            return None

    def check_user_exists_in_master(self, user_id):
        """Check if a user exists in the master_user_data collection"""
        try:
            # Query the master_user_data collection for the user_id
            result = self.db.db.master_user_data.find_one({'user_id': user_id})
            return result is not None
        except Exception as e:
            self.logger.error(f"Error checking if user {user_id} exists in master_user_data: {str(e)}")
            return False

    def get_master_user_data(self, user_id, context=None):
        """Get user data from the master_user_data collection"""
        try:
            # Get the appropriate database for the current tenant
            db = self.get_tenant_database(context)

            # Query the master_user_data collection for the user_id
            self.logger.info(f"Querying master_user_data for user_id: {user_id}")
            # Try to find the user with user_id as is
            result = db.db.master_user_data.find_one({'user_id': user_id})

            # If not found, try converting user_id to int or str
            if not result:
                if isinstance(user_id, str) and user_id.isdigit():
                    self.logger.info(f"Trying to find user with user_id as int: {int(user_id)}")
                    result = db.db.master_user_data.find_one({'user_id': int(user_id)})
                elif isinstance(user_id, int):
                    self.logger.info(f"Trying to find user with user_id as str: {str(user_id)}")
                    result = db.db.master_user_data.find_one({'user_id': str(user_id)})

            self.logger.info(f"Query result for user_id {user_id}: {result}")

            if result:
                # Convert ObjectId to string for serialization
                result['_id'] = str(result['_id'])
                self.logger.info(f"Returning user data for user_id {user_id}: {result}")
            else:
                self.logger.info(f"No user data found for user_id {user_id}")
            return result
        except Exception as e:
            self.logger.error(f"Error getting master user data for user {user_id}: {str(e)}")
            return None

    def save_to_master_user_data(self, user_data, context=None):
        """Save user data to the master_user_data collection

        Returns:
            If the access code is valid and the user is automatically verified:
                (bool, bool): (success, auto_verified)
            Otherwise:
                bool: success
        """
        try:
            # Get the appropriate database for the current tenant
            db = self.get_tenant_database(context)

            # Ensure user_id is present
            if 'user_id' not in user_data:
                self.logger.error("Cannot save to master_user_data: user_id is missing")
                return False

            self.logger.info(f"Saving user data to master_user_data: {user_data}")

            # Add last_updated field
            user_data['last_updated'] = datetime.now(timezone.utc)

            # Initialize notification_history if not present
            if 'notification_history' not in user_data:
                user_data['notification_history'] = ""

            # Add user_status field based on broker_reg, user_verify, and access_code
            if 'access_code' in user_data and user_data.get('access_code'):
                if user_data.get('broker_reg') == True:
                    if user_data.get('user_verify') == True:
                        user_data['user_status'] = 'active'  # Verified user with broker registration
                    else:
                        user_data['user_status'] = 'pending_verification'  # Unverified user with broker registration
                else:
                    user_data['user_status'] = 'incomplete'  # Has access code but no broker registration
            else:
                user_data['user_status'] = 'new'  # No access code

            # Update or insert the document
            result = db.db.master_user_data.update_one(
                {'user_id': user_data['user_id']},
                {'$set': user_data},
                upsert=True
            )

            success = result.modified_count > 0 or result.upserted_id is not None
            self.logger.info(f"Save result: modified_count={result.modified_count}, upserted_id={result.upserted_id}, success={success}")

            # Process the user data based on access code and verification status
            if success and 'access_code' in user_data:
                user_id = user_data['user_id']
                access_code = user_data['access_code']

                # Check if the access code is valid in our system and not used by another user
                is_valid_code = self.verify_access_code(access_code, user_id, context)

                # Update broker_reg based on access code validity
                if not is_valid_code:
                    # If the access code is not valid, set broker_reg to False
                    self.logger.info(f"Access code {access_code} is not valid. Setting broker_reg to False for user {user_id}")

                    update_result = db.db.master_user_data.update_one(
                        {'user_id': user_id},
                        {'$set': {
                            'broker_reg': False,
                            'user_status': 'incomplete',  # Has access code but no broker registration
                            'user_verify_status': 'pending',
                            'last_updated': datetime.now(timezone.utc)
                        }}
                    )

                    if update_result.modified_count > 0:
                        self.logger.info(f"Successfully updated broker_reg to False for user {user_id}")
                    else:
                        self.logger.error(f"Failed to update broker_reg for user {user_id}")

                # Now handle verification based on access code validity and user_verify
                # Get the updated user data
                updated_user_data = self.get_master_user_data(user_id, context)

                if updated_user_data:
                    if is_valid_code and updated_user_data.get('user_verify') == False:
                        # If the access code is valid and user needs verification, automatically verify the user
                        self.logger.info(f"Access code {access_code} is valid. Automatically verifying user {user_id}")

                        # Double-check that the access code is still not used by another user
                        # This helps prevent race conditions where two users try to use the same code simultaneously
                        existing_users = self.get_users_by_code(access_code, context)
                        self.logger.info(f"Existing users with access code {access_code}: {existing_users}")

                        # Check if there are multiple users with this access code
                        # OR if the current user is not in the list of existing users
                        code_used_by_other = len(existing_users) > 1 or (existing_users and str(user_id) not in [str(u) for u in existing_users])
                        self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

                        if code_used_by_other:
                            # The code has been claimed by another user while this user was in the process
                            self.logger.warning(f"Access code {access_code} was claimed by another user while user {user_id} was being verified")

                            # Update the user record to indicate the access code is invalid
                            update_result = db.db.master_user_data.update_one(
                                {'user_id': user_id},
                                {'$set': {
                                    'access_code': None,
                                    'broker_reg': False,
                                    'user_status': 'incomplete',  # Has access code but no broker registration
                                    'user_verify_status': 'pending',
                                    'last_updated': datetime.now(timezone.utc)
                                }}
                            )
                            return success, False  # Return success and auto_verified=False

                        # If we get here, the code is still available for this user

                        update_result = db.db.master_user_data.update_one(
                            {'user_id': user_id},
                            {'$set': {
                                'user_verify': True,
                                'subscription': 'active',
                                'broker_reg': True,  # Set broker_reg to True since the access code is valid
                                'user_status': 'active',
                                'user_verify_status': 'approved',
                                'notification_history': "",
                                'added_at': datetime.now(timezone.utc),
                                'last_updated': datetime.now(timezone.utc)
                            }}
                        )

                        if update_result.modified_count > 0:
                            self.logger.info(f"Successfully verified user {user_id} with access code {access_code}")
                            # Return a flag indicating that the user was automatically verified
                            return success, True  # Return success and auto_verified flag
                        else:
                            self.logger.error(f"Failed to verify user {user_id}")
                    elif not is_valid_code and updated_user_data.get('user_verify') == False:
                        # If the access code is not valid and user needs verification, create a verification request
                        self.logger.info(f"User {user_id} needs verification and has an invalid access code. Creating verification request.")

                        # Check if a verification request already exists for this user
                        existing_requests = list(self.db.db.verification_request.find({'user_id': user_id}))

                        if not existing_requests:
                            # Create user details dictionary from user_data
                            user_details = {
                                'name': updated_user_data.get('name', 'Unknown'),
                                'email': updated_user_data.get('email', 'Unknown'),
                                'whatsapp': updated_user_data.get('whatsapp', 'Unknown'),
                                'trading_experience': updated_user_data.get('trading_experience', 'Unknown')
                            }

                            # Create the verification request
                            request_id, request_success = self.create_verification_request(user_id, access_code, user_details)

                            if request_success:
                                self.logger.info(f"Successfully created verification request {request_id} for user {user_id}")
                            else:
                                self.logger.error(f"Failed to create verification request for user {user_id}")

            return success, False  # Return success and auto_verified=False
        except Exception as e:
            self.logger.error(f"Error saving to master_user_data: {str(e)}")
            return False, False  # Return failure and auto_verified=False

    # User verification methods
    def verify_user(self, user_id, verify=True, telegram_bot_id=None):
        """Set the user_verify field for a user

        Args:
            user_id: The user's Telegram ID
            verify: Whether to verify (True) or unverify (False) the user
            telegram_bot_id: Optional Telegram bot ID that processed the verification

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the user data
            user_data = self.get_master_user_data(user_id)
            if not user_data:
                self.logger.error(f"Cannot verify user {user_id}: user not found")
                return False

            # Prepare update data
            update_data = {
                'user_verify': verify,
                'last_updated': datetime.now(timezone.utc)
            }

            # If verifying the user, also set subscription to active and update user_status
            if verify:
                update_data['subscription'] = 'active'
                update_data['user_status'] = 'active'  # Verified user with active subscription
                update_data['notification_history'] = ""  # Initialize notification_history
            else:
                # If unverifying, set user_status to pending_verification if they have broker_reg
                user_data = self.get_master_user_data(user_id)
                if user_data and user_data.get('broker_reg') == True:
                    update_data['user_status'] = 'pending_verification'

            # Add telegram_bot_id if provided
            if telegram_bot_id:
                update_data['telegram_bot_id'] = telegram_bot_id
            # Preserve existing telegram_bot_id if not provided
            elif user_data and 'telegram_bot_id' in user_data:
                update_data['telegram_bot_id'] = user_data['telegram_bot_id']

            # Update the user_verify field and possibly subscription status
            result = self.db.db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': update_data}
            )

            success = result.modified_count > 0
            if success:
                status = "verified" if verify else "unverified"
                if verify:
                    self.logger.info(f"User {user_id} {status} successfully and subscription set to active")
                else:
                    self.logger.info(f"User {user_id} {status} successfully")
            else:
                self.logger.warning(f"Failed to update verification status for user {user_id}")

            return success
        except Exception as e:
            self.logger.error(f"Error verifying user {user_id}: {str(e)}")
            return False

    def get_pending_verification_users(self):
        """Get all users with active subscription but not verified

        Returns:
            list: List of user data dictionaries
        """
        try:
            # Query for users with active subscription but not verified
            users = list(self.db.db.master_user_data.find({
                'subscription': 'active',
                'user_verify': False
            }))

            # Convert ObjectId to string for serialization
            for user in users:
                if '_id' in user:
                    user['_id'] = str(user['_id'])

            self.logger.info(f"Found {len(users)} users pending verification")
            return users
        except Exception as e:
            self.logger.error(f"Error getting pending verification users: {str(e)}")
            return []

    def update_subscription_status(self, user_id, status, telegram_bot_id=None):
        """Update a user's subscription status

        Args:
            user_id: The user's Telegram ID
            status: The new subscription status ('active', 'pending', 'inactive')
            telegram_bot_id: Optional Telegram bot ID that processed the status update

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the user data
            user_data = self.get_master_user_data(user_id)
            if not user_data:
                self.logger.error(f"Cannot update subscription status for user {user_id}: user not found")
                return False

            # Prepare update data
            update_data = {
                'subscription': status,
                'last_updated': datetime.now(timezone.utc)
            }

            # Initialize notification_history if not present
            if 'notification_history' not in user_data:
                update_data['notification_history'] = ""

            # Add telegram_bot_id if provided
            if telegram_bot_id:
                update_data['telegram_bot_id'] = telegram_bot_id
            # Preserve existing telegram_bot_id if not provided
            elif user_data and 'telegram_bot_id' in user_data:
                update_data['telegram_bot_id'] = user_data['telegram_bot_id']

            # Update user_status based on subscription status and user_verify
            if status == 'active':
                # Check if user is verified
                if user_data.get('user_verify') == True:
                    update_data['user_status'] = 'active'  # Active subscription and verified
                else:
                    update_data['user_status'] = 'pending_verification'  # Active subscription but not verified
            elif status == 'inactive':
                if user_data.get('broker_reg') == True:
                    update_data['user_status'] = 'inactive'  # Inactive subscription with broker registration
                else:
                    update_data['user_status'] = 'new'  # Inactive subscription without broker registration
            elif status == 'pending':
                update_data['user_status'] = 'pending_verification'  # Pending subscription

            # Update the subscription status
            result = self.db.db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': update_data}
            )

            success = result.modified_count > 0
            if success:
                self.logger.info(f"User {user_id} subscription status updated to {status}")
            else:
                self.logger.warning(f"Failed to update subscription status for user {user_id}")

            return success
        except Exception as e:
            self.logger.error(f"Error updating subscription status for user {user_id}: {str(e)}")
            return False

    def update_broker_reg(self, user_id, is_registered, access_code=None, telegram_bot_id=None):
        """Update a user's broker registration status

        Args:
            user_id: The user's Telegram ID
            is_registered: Whether the user is registered with a broker
            access_code: The access code to set (if provided)
            telegram_bot_id: Optional Telegram bot ID that processed the registration update

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the user data
            user_data = self.get_master_user_data(user_id)
            if not user_data:
                self.logger.error(f"Cannot update broker registration for user {user_id}: user not found")
                return False

            # Prepare update data
            update_data = {
                'broker_reg': is_registered,
                'last_updated': datetime.now(timezone.utc)
            }

            # Initialize notification_history if not present
            if 'notification_history' not in user_data:
                update_data['notification_history'] = ""

            # Add telegram_bot_id if provided
            if telegram_bot_id:
                update_data['telegram_bot_id'] = telegram_bot_id
            # Preserve existing telegram_bot_id if not provided
            elif user_data and 'telegram_bot_id' in user_data:
                update_data['telegram_bot_id'] = user_data['telegram_bot_id']

            # Add access code if provided
            if access_code:
                update_data['access_code'] = access_code

            # Update user_status based on broker_reg, user_verify, and access_code
            if is_registered:
                if user_data.get('user_verify') == True:
                    update_data['user_status'] = 'active'  # Registered with broker and verified
                else:
                    update_data['user_status'] = 'pending_verification'  # Registered with broker but not verified
            else:
                if access_code:
                    update_data['user_status'] = 'incomplete'  # Not registered with broker but has access code
                else:
                    update_data['user_status'] = 'new'  # Not registered with broker and no access code

            # Update the broker_reg field
            result = self.db.db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': update_data}
            )

            success = result.modified_count > 0
            if success:
                status = "registered" if is_registered else "unregistered"
                self.logger.info(f"User {user_id} broker registration status updated to {status}")
            else:
                self.logger.warning(f"Failed to update broker registration status for user {user_id}")

            return success
        except Exception as e:
            self.logger.error(f"Error updating broker registration status for user {user_id}: {str(e)}")
            return False

    # Support Request methods
    def create_support_request(self, user_id, initial_message, user_details=None, telegram_bot_id=None):
        """Create a new support request in the support_request collection

        Args:
            user_id: The user's Telegram ID
            initial_message: The initial message from the user
            user_details: Optional dictionary with user details (name, email, etc.)
            telegram_bot_id: Optional Telegram bot ID that received the request

        Returns:
            tuple: (request_id, success)
        """
        try:
            # Generate a unique request ID
            request_id = f"REQ-{user_id}-{int(datetime.now(timezone.utc).timestamp())}"

            # Create the request document
            request_doc = {
                'request_id': request_id,
                'user_id': user_id,
                'status': 'pending',
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc),
                'messages': [
                    {
                        'text': initial_message,
                        'timestamp': datetime.now(timezone.utc),
                        'type': 'initial'
                    }
                ],
                'is_resolved': False
            }

            # Add user details if provided
            if user_details:
                request_doc['user_details'] = user_details

            # Add telegram_bot_id if provided
            if telegram_bot_id:
                request_doc['telegram_bot_id'] = telegram_bot_id

            # Insert the document
            result = self.db.db.support_request.insert_one(request_doc)

            if result.inserted_id:
                self.logger.info(f"Created support request {request_id} for user {user_id}")
                return request_id, True
            else:
                self.logger.error(f"Failed to create support request for user {user_id}")
                return None, False
        except Exception as e:
            self.logger.error(f"Error creating support request: {str(e)}")
            return None, False

    def add_message_to_support_request(self, request_id, message_text):
        """Add a message to an existing support request"""
        try:
            # Update the request document
            result = self.db.db.support_request.update_one(
                {'request_id': request_id},
                {
                    '$push': {
                        'messages': {
                            'text': message_text,
                            'timestamp': datetime.now(timezone.utc),
                            'type': 'additional'
                        }
                    },
                    '$set': {
                        'updated_at': datetime.now(timezone.utc)
                    }
                }
            )

            if result.modified_count > 0:
                self.logger.info(f"Added message to support request {request_id}")
                return True
            else:
                self.logger.error(f"Failed to add message to support request {request_id}")
                return False
        except Exception as e:
            self.logger.error(f"Error adding message to support request: {str(e)}")
            return False

    def get_support_request(self, request_id):
        """Get a support request by ID"""
        try:
            request = self.db.db.support_request.find_one({'request_id': request_id})
            if request:
                # Convert ObjectId to string for serialization
                request['_id'] = str(request['_id'])
            return request
        except Exception as e:
            self.logger.error(f"Error getting support request {request_id}: {str(e)}")
            return None

    def get_user_support_requests(self, user_id):
        """Get all support requests for a user"""
        try:
            requests = list(self.db.db.support_request.find({'user_id': user_id}).sort('created_at', -1))
            # Convert ObjectId to string for serialization
            for request in requests:
                request['_id'] = str(request['_id'])
            return requests
        except Exception as e:
            self.logger.error(f"Error getting support requests for user {user_id}: {str(e)}")
            return []

    def get_all_support_requests(self, status=None, limit=100, skip=0):
        """Get all support requests, optionally filtered by status"""
        try:
            query = {}
            if status:
                query['status'] = status

            requests = list(self.db.db.support_request.find(query).sort('created_at', -1).skip(skip).limit(limit))
            # Convert ObjectId to string for serialization
            for request in requests:
                request['_id'] = str(request['_id'])
            return requests
        except Exception as e:
            self.logger.error(f"Error getting all support requests: {str(e)}")
            return []

    def update_support_request_status(self, request_id, status):
        """Update the status of a support request"""
        try:
            result = self.db.db.support_request.update_one(
                {'request_id': request_id},
                {
                    '$set': {
                        'status': status,
                        'updated_at': datetime.now(timezone.utc),
                        'is_resolved': status == 'resolved'
                    }
                }
            )

            if result.modified_count > 0:
                self.logger.info(f"Updated status of support request {request_id} to {status}")
                return True
            else:
                self.logger.error(f"Failed to update status of support request {request_id}")
                return False
        except Exception as e:
            self.logger.error(f"Error updating support request status: {str(e)}")
            return False

    # Verification Request methods
    def create_verification_request(self, user_id, access_code, user_details, telegram_bot_id=None):
        """Create a new verification request in the verification_request collection

        Args:
            user_id: The user's Telegram ID
            access_code: The access code to verify
            user_details: Dictionary with user details (name, email, etc.)
            telegram_bot_id: Optional Telegram bot ID that received the verification request

        Returns:
            tuple: (request_id, success)
        """
        try:
            # First, check if the user actually needs verification
            user_data = self.get_master_user_data(user_id)

            if user_data and user_data.get('user_verify') == True:
                self.logger.info(f"User {user_id} is already verified. Skipping verification request creation.")
                return None, False

            # Check if the access code is valid and not used by another user
            is_valid_code = self.verify_access_code(access_code, user_id)

            # If the access code is valid, we should auto-verify the user instead of creating a request
            if is_valid_code:
                self.logger.info(f"Access code {access_code} is valid. Auto-verifying user {user_id} instead of creating a verification request.")

                # Double-check that the access code is still not used by another user
                # This helps prevent race conditions where two users try to use the same code simultaneously
                existing_users = self.get_users_by_code(access_code)
                self.logger.info(f"Existing users with access code {access_code}: {existing_users}")

                # Check if there are multiple users with this access code
                # OR if the current user is not in the list of existing users
                code_used_by_other = len(existing_users) > 1 or (existing_users and str(user_id) not in [str(u) for u in existing_users])
                self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

                if code_used_by_other:
                    # The code has been claimed by another user while this user was in the process
                    self.logger.warning(f"Access code {access_code} was claimed by another user while user {user_id} was being verified")

                    # Update the user record to indicate the access code is invalid
                    if user_data:
                        update_data = {
                            'broker_reg': False,
                            'user_status': 'incomplete',  # Has access code but no broker registration
                            'user_verify_status': 'pending',
                            'last_updated': datetime.now(timezone.utc)
                        }

                        # Add telegram_bot_id if provided
                        if telegram_bot_id:
                            update_data['telegram_bot_id'] = telegram_bot_id
                        # Preserve existing telegram_bot_id if not provided
                        elif user_data and 'telegram_bot_id' in user_data:
                            update_data['telegram_bot_id'] = user_data['telegram_bot_id']

                        self.db.db.master_user_data.update_one(
                            {'user_id': user_id},
                            {'$set': update_data}
                        )

                    # Return failure - the code is already used by another user
                    self.logger.info(f"Access code {access_code} is already used by another user. Verification failed for user {user_id}")
                    return None, False
                else:
                    # If we get here, the code is still available for this user
                    # Update the user data to set user_verify to True and subscription to active
                    update_data = {
                        'user_verify': True,
                        'subscription': 'active',
                        'broker_reg': True,  # Set broker_reg to True since the access code is valid
                        'user_status': 'active',
                        'user_verify_status': 'approved',
                        'notification_history': "",
                        'last_updated': datetime.now(timezone.utc)
                    }

                    # Add telegram_bot_id if provided
                    if telegram_bot_id:
                        update_data['telegram_bot_id'] = telegram_bot_id
                    # Preserve existing telegram_bot_id if not provided
                    elif user_data and 'telegram_bot_id' in user_data:
                        update_data['telegram_bot_id'] = user_data['telegram_bot_id']

                    update_result = self.db.db.master_user_data.update_one(
                        {'user_id': user_id},
                        {'$set': update_data}
                    )

                    if update_result.modified_count > 0:
                        self.logger.info(f"Successfully auto-verified user {user_id} with access code {access_code}")
                        return "AUTO-VERIFIED", True

            # If the access code is not valid, update broker_reg to False
            if not is_valid_code and user_data:
                update_data = {
                    'broker_reg': False,
                    'user_status': 'incomplete',
                    'user_verify_status': 'pending',
                    'last_updated': datetime.now(timezone.utc)
                }

                # Add telegram_bot_id if provided
                if telegram_bot_id:
                    update_data['telegram_bot_id'] = telegram_bot_id
                # Preserve existing telegram_bot_id if not provided
                elif user_data and 'telegram_bot_id' in user_data:
                    update_data['telegram_bot_id'] = user_data['telegram_bot_id']

                self.db.db.master_user_data.update_one(
                    {'user_id': user_id},
                    {'$set': update_data}
                )

            # Generate a unique request ID
            request_id = f"VRFY-{user_id}-{int(datetime.now(timezone.utc).timestamp())}"

            # Create the request document
            request_doc = {
                'request_id': request_id,
                'user_id': user_id,
                'access_code': access_code,
                'status': 'pending',
                'verified': False,  # Initially set to false until admin verifies
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc),
                'user_details': user_details,
                'admin_notes': [],
                'verification_history': [
                    {
                        'status': 'pending',
                        'timestamp': datetime.now(timezone.utc),
                        'note': 'Verification request submitted'
                    }
                ]
            }

            # Add telegram_bot_id if provided
            if telegram_bot_id:
                request_doc['telegram_bot_id'] = telegram_bot_id

            # Insert the document
            result = self.db.db.verification_request.insert_one(request_doc)

            if result.inserted_id:
                self.logger.info(f"Created verification request {request_id} for user {user_id}")
                return request_id, True
            else:
                self.logger.error(f"Failed to create verification request for user {user_id}")
                return None, False
        except Exception as e:
            self.logger.error(f"Error creating verification request: {str(e)}")
            return None, False

    def get_verification_request(self, request_id):
        """Get a verification request by ID"""
        try:
            request = self.db.db.verification_request.find_one({'request_id': request_id})
            if request:
                # Convert ObjectId to string for serialization
                request['_id'] = str(request['_id'])
            return request
        except Exception as e:
            self.logger.error(f"Error getting verification request {request_id}: {str(e)}")
            return None

    def get_user_verification_requests(self, user_id):
        """Get all verification requests for a user"""
        try:
            requests = list(self.db.db.verification_request.find({'user_id': user_id}).sort('created_at', -1))
            # Convert ObjectId to string for serialization
            for request in requests:
                request['_id'] = str(request['_id'])
            return requests
        except Exception as e:
            self.logger.error(f"Error getting verification requests for user {user_id}: {str(e)}")
            return []

    def get_all_verification_requests(self, status=None, verified=None, limit=100, skip=0):
        """Get all verification requests, optionally filtered by status and verified flag"""
        try:
            query = {}
            if status:
                query['status'] = status
            if verified is not None:
                query['verified'] = verified

            requests = list(self.db.db.verification_request.find(query).sort('created_at', -1).skip(skip).limit(limit))
            # Convert ObjectId to string for serialization
            for request in requests:
                request['_id'] = str(request['_id'])
            return requests
        except Exception as e:
            self.logger.error(f"Error getting all verification requests: {str(e)}")
            return []

    def update_verification_request_status(self, request_id, status, note=None, verified=None):
        """Update the status of a verification request"""
        try:
            update_doc = {
                'status': status,
                'updated_at': datetime.now(timezone.utc)
            }

            # Add verified flag if provided
            if verified is not None:
                update_doc['verified'] = verified

            # Create history entry
            history_entry = {
                'status': status,
                'timestamp': datetime.now(timezone.utc)
            }

            # Add note if provided
            if note:
                history_entry['note'] = note
                # Also add to admin_notes array
                self.db.db.verification_request.update_one(
                    {'request_id': request_id},
                    {
                        '$push': {
                            'admin_notes': {
                                'note': note,
                                'timestamp': datetime.now(timezone.utc)
                            }
                        }
                    }
                )

            # Update the document
            result = self.db.db.verification_request.update_one(
                {'request_id': request_id},
                {
                    '$set': update_doc,
                    '$push': {
                        'verification_history': history_entry
                    }
                }
            )

            if result.modified_count > 0:
                self.logger.info(f"Updated status of verification request {request_id} to {status}")
                return True
            else:
                self.logger.error(f"Failed to update status of verification request {request_id}")
                return False
        except Exception as e:
            self.logger.error(f"Error updating verification request status: {str(e)}")
            return False

    def export_users_to_csv(self, file_path=None):
        """Export all user data to CSV format"""
        try:
            result = self.db.export_users_to_csv(file_path)
            if file_path and result:
                self.logger.info(f"Exported user data to {file_path}")
            return result
        except Exception as e:
            self.logger.error(f"Error exporting users to CSV: {str(e)}")
            return False if file_path else ""