"""
Logging utilities for tenant-aware logging throughout the application.
"""

import logging
from typing import Optional


class TenantAwareFormatter(logging.Formatter):
    """Custom formatter that includes tenant name in log messages"""
    
    def __init__(self, fmt=None, tenant_name="GLOBAL"):
        super().__init__(fmt)
        self.tenant_name = tenant_name
    
    def format(self, record):
        # Add tenant_name to the log record
        record.tenant_name = self.tenant_name
        return super().format(record)


def get_tenant_logger(name: str, tenant_name: Optional[str] = None) -> logging.Logger:
    """
    Get a tenant-aware logger.
    
    Args:
        name: Logger name (usually __name__ or component name)
        tenant_name: Optional tenant name for multi-tenant logging
        
    Returns:
        Logger instance configured for tenant-aware logging
    """
    if tenant_name:
        logger_name = f"{name}.{tenant_name}"
    else:
        logger_name = name
    
    return logging.getLogger(logger_name)


def setup_tenant_logging(tenant_name: str, log_config: dict) -> logging.Logger:
    """
    Set up logging for a specific tenant.
    
    Args:
        tenant_name: Name of the tenant
        log_config: Logging configuration dictionary
        
    Returns:
        Configured logger for the tenant
    """
    from logging.handlers import RotatingFileHandler
    import os
    
    # Extract configuration
    log_file = log_config.get('log_file', './logs/bot.log')
    log_level_str = log_config.get('level', 'INFO')
    log_format = log_config.get('format', '%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s')
    max_size_mb = log_config.get('max_size_mb', 5)
    backup_count = log_config.get('backup_count', 3)
    
    # Convert string log level to logging constant
    log_level = getattr(logging, log_level_str.upper(), logging.INFO)
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Create tenant-specific logger
    logger_name = f"telegram_bot.{tenant_name}"
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)
    
    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create file handler for logging to file
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=max_size_mb * 1024 * 1024,
        backupCount=backup_count
    )
    file_handler.setLevel(log_level)
    
    # Create console handler for logging to console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # Create tenant-aware formatter
    formatter = TenantAwareFormatter(log_format, tenant_name)
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def log_with_tenant(logger: logging.Logger, level: str, message: str, tenant_name: Optional[str] = None, **kwargs):
    """
    Log a message with tenant context.
    
    Args:
        logger: Logger instance
        level: Log level ('info', 'error', 'warning', 'debug')
        message: Log message
        tenant_name: Optional tenant name to include in the message
        **kwargs: Additional keyword arguments for the log message
    """
    # If tenant_name is provided, include it in the message
    if tenant_name:
        message = f"[{tenant_name}] {message}"
    
    # Get the logging method
    log_method = getattr(logger, level.lower(), logger.info)
    
    # Log the message
    log_method(message, **kwargs)


def create_tenant_context_logger(base_logger: logging.Logger, tenant_name: str) -> logging.Logger:
    """
    Create a logger that automatically includes tenant context.
    
    Args:
        base_logger: Base logger instance
        tenant_name: Tenant name to include in all log messages
        
    Returns:
        Logger wrapper that includes tenant context
    """
    class TenantContextLogger:
        def __init__(self, logger, tenant):
            self._logger = logger
            self._tenant = tenant
        
        def _log_with_context(self, level, message, *args, **kwargs):
            # Add tenant context to the message
            tenant_message = f"[{self._tenant}] {message}"
            log_method = getattr(self._logger, level)
            log_method(tenant_message, *args, **kwargs)
        
        def info(self, message, *args, **kwargs):
            self._log_with_context('info', message, *args, **kwargs)
        
        def error(self, message, *args, **kwargs):
            self._log_with_context('error', message, *args, **kwargs)
        
        def warning(self, message, *args, **kwargs):
            self._log_with_context('warning', message, *args, **kwargs)
        
        def debug(self, message, *args, **kwargs):
            self._log_with_context('debug', message, *args, **kwargs)
        
        def critical(self, message, *args, **kwargs):
            self._log_with_context('critical', message, *args, **kwargs)
        
        # Delegate other attributes to the original logger
        def __getattr__(self, name):
            return getattr(self._logger, name)
    
    return TenantContextLogger(base_logger, tenant_name)
