"""
Message handling utilities for the Telegram bot.
Centralizes common message operations and editing logic.
"""

import logging
import asyncio
from typing import Optional, Dict, Set
from telegram import Message
from telegram.error import TelegramError, TimedOut, NetworkError


class MessageHelpers:
    """Helper utilities for message operations"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the message helpers"""
        self.logger = logger or logging.getLogger(__name__)

        # Anti-spam protection: track recent button clicks per user
        self._recent_clicks: Dict[int, float] = {}  # user_id -> last_click_time
        self._processing_users: Set[int] = set()  # users currently being processed

        # Timeout settings
        self.default_timeout = 10  # seconds for regular operations
        self.image_timeout = 15    # seconds for image operations
        self.retry_attempts = 2    # number of retry attempts

    def is_user_spamming(self, user_id: int, min_interval: float = 1.0) -> bool:
        """
        Check if user is clicking buttons too quickly (anti-spam protection).

        Args:
            user_id: Telegram user ID
            min_interval: Minimum seconds between clicks

        Returns:
            True if user is spamming, False otherwise
        """
        import time
        current_time = time.time()

        if user_id in self._recent_clicks:
            time_since_last = current_time - self._recent_clicks[user_id]
            if time_since_last < min_interval:
                self.logger.warning(f"User {user_id} clicking too fast: {time_since_last:.2f}s since last click")
                return True

        self._recent_clicks[user_id] = current_time
        return False

    def is_user_being_processed(self, user_id: int) -> bool:
        """
        Check if user's request is currently being processed.

        Args:
            user_id: Telegram user ID

        Returns:
            True if user is being processed, False otherwise
        """
        return user_id in self._processing_users

    async def with_user_processing_lock(self, user_id: int, operation):
        """
        Execute operation with user processing lock to prevent duplicate requests.

        Args:
            user_id: Telegram user ID
            operation: Async function to execute

        Returns:
            Result of operation or None if user is already being processed
        """
        if self.is_user_being_processed(user_id):
            self.logger.info(f"User {user_id} request already being processed, ignoring duplicate")
            return None

        try:
            self._processing_users.add(user_id)
            self.logger.info(f"Started processing for user {user_id}")
            result = await operation()
            return result
        finally:
            self._processing_users.discard(user_id)
            self.logger.info(f"Finished processing for user {user_id}")

    async def edit_message_safely(self, message: Message, text: str, reply_markup=None) -> bool:
        """
        Edit a message safely with timeout handling and retry logic.

        Args:
            message: Message object to edit
            text: New text content
            reply_markup: Optional keyboard markup

        Returns:
            True if successful, False otherwise
        """
        for attempt in range(self.retry_attempts + 1):
            try:
                if message.photo:
                    self.logger.info(f"Editing caption for message with photo (attempt {attempt + 1})")
                    await asyncio.wait_for(
                        message.edit_caption(caption=text, reply_markup=reply_markup),
                        timeout=self.default_timeout
                    )
                else:
                    self.logger.info(f"Editing text message (attempt {attempt + 1})")
                    await asyncio.wait_for(
                        message.edit_text(text=text, reply_markup=reply_markup),
                        timeout=self.default_timeout
                    )

                self.logger.info(f"Message edited successfully (attempt {attempt + 1})")
                return True

            except (TimedOut, NetworkError, asyncio.TimeoutError) as e:
                if attempt < self.retry_attempts:
                    wait_time = (attempt + 1) * 2  # Exponential backoff: 2s, 4s, 6s
                    self.logger.warning(f"Edit timeout/network error (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(f"Failed to edit message after {self.retry_attempts + 1} attempts: {e}")
                    return False

            except Exception as e:
                self.logger.error(f"Failed to edit message (attempt {attempt + 1}): {e}")
                if attempt < self.retry_attempts:
                    await asyncio.sleep(1)
                    continue
                return False

        return False

    async def update_message_with_image(self, message: Message, text: str, image_manager, image_type: str, reply_markup=None, context=None) -> bool:
        """
        Update a message with an image, providing smooth transitions.

        This method handles the complex case of switching between text and image messages.
        It will ALWAYS replace the message with the specified image type.

        Args:
            message: Message object to update
            text: New text content
            image_manager: ImageManager instance
            image_type: Type of image to use
            reply_markup: Optional keyboard markup
            context: Bot context (required for some operations)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if we can get the image path first
            image_path = image_manager.get_image_path(image_type)
            if not image_path:
                self.logger.warning(f"No image found for type {image_type}, falling back to text edit")
                return await self.edit_message_safely(message, text, reply_markup)

            self.logger.info(f"Replacing message with {image_type} image")

            # Always send a new message with the specified image
            # This ensures we get the correct image regardless of current message state
            new_message = await image_manager.send_message_with_image(
                message, text, image_type, reply_markup, context
            )

            if new_message:
                # Only delete the old message after the new one is successfully sent
                try:
                    pass
                    return True
                except Exception as delete_error:
                    self.logger.warning(f"Failed to delete old message: {delete_error}")
                    # Even if deletion fails, the new message was sent successfully
                    return True
            else:
                self.logger.error(f"Failed to send new message with {image_type} image")
                return False

        except Exception as e:
            self.logger.error(f"Unexpected error in update_message_with_image: {str(e)}")
            return False

    async def update_message_smartly(self, message: Message, text: str, reply_markup=None,
                                   image_manager=None, image_type: str = None, context=None) -> bool:
        """
        Smart message updating that chooses the best strategy based on content.

        This is the main method to use for all message updates. It automatically
        chooses between editing and replacing based on the content type.

        Args:
            message: Message object to update
            text: New text content
            reply_markup: Optional keyboard markup
            image_manager: Optional ImageManager for image operations
            image_type: Optional image type if switching to image
            context: Optional bot context

        Returns:
            True if successful, False otherwise
        """
        try:
            # If no image is requested, just edit the text
            if not image_type or not image_manager:
                return await self.edit_message_safely(message, text, reply_markup)

            # If image is requested, use the image update method
            return await self.update_message_with_image(
                message, text, image_manager, image_type, reply_markup, context
            )

        except Exception as e:
            self.logger.error(f"Error in smart message update: {str(e)}")
            return False

    def get_bot_id_safely(self, update_or_query) -> Optional[int]:
        """
        Get the bot ID safely from an update or query object.

        Args:
            update_or_query: Either a telegram.Update or telegram.CallbackQuery object

        Returns:
            Bot ID or None if not found
        """
        try:
            # Try different ways to get the bot ID
            if hasattr(update_or_query, 'message') and update_or_query.message:
                if hasattr(update_or_query.message, 'bot') and update_or_query.message.bot:
                    return update_or_query.message.bot.id

            if hasattr(update_or_query, '_bot') and update_or_query._bot:
                return update_or_query._bot.id

            if hasattr(update_or_query, 'bot') and update_or_query.bot:
                return update_or_query.bot.id

            self.logger.warning("Could not get bot ID from update/query object")
            return None

        except Exception as e:
            self.logger.error(f"Error getting bot ID: {str(e)}")
            return None

    def extract_user_info(self, update) -> dict:
        """
        Extract user information from an update object.

        Args:
            update: Telegram update object

        Returns:
            Dictionary with user information
        """
        try:
            user_info = {}

            if hasattr(update, 'message') and update.message and update.message.from_user:
                user = update.message.from_user
            elif hasattr(update, 'callback_query') and update.callback_query and update.callback_query.from_user:
                user = update.callback_query.from_user
            else:
                self.logger.warning("Could not extract user from update")
                return {}

            user_info['user_id'] = user.id
            user_info['username'] = user.username or "Unknown"
            user_info['first_name'] = user.first_name or ""
            user_info['last_name'] = user.last_name or ""
            user_info['full_name'] = f"{user_info['first_name']} {user_info['last_name']}".strip()

            return user_info

        except Exception as e:
            self.logger.error(f"Error extracting user info: {str(e)}")
            return {}

    def validate_text_input(self, text: str, min_length: int = 1, max_length: int = 1000) -> tuple[bool, str]:
        """
        Validate text input with length constraints.

        Args:
            text: Text to validate
            min_length: Minimum allowed length
            max_length: Maximum allowed length

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not text or not text.strip():
            return False, "Text cannot be empty"

        text = text.strip()

        if len(text) < min_length:
            return False, f"Text must be at least {min_length} characters long"

        if len(text) > max_length:
            return False, f"Text cannot exceed {max_length} characters"

        return True, ""

    def sanitize_text_for_markdown(self, text: str) -> str:
        """
        Sanitize text for safe use with Telegram's Markdown parsing.

        Args:
            text: Text to sanitize

        Returns:
            Sanitized text
        """
        if not text:
            return ""

        # Escape special Markdown characters
        special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']

        for char in special_chars:
            text = text.replace(char, f'\\{char}')

        return text

    async def send_error_message(self, message: Message, error_text: str = "An error occurred. Please try again.") -> Optional[Message]:
        """
        Send a standardized error message.

        Args:
            message: Message object to reply to
            error_text: Error message text

        Returns:
            Sent message object or None if failed
        """
        try:
            return await message.reply_text(
                f"❌ {error_text}",
                parse_mode=None  # Don't use markdown for error messages
            )
        except Exception as e:
            self.logger.error(f"Failed to send error message: {str(e)}")
            return None
