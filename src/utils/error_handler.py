"""
Error handling utilities for the Telegram bot.
Provides standardized error handling patterns and logging.
"""

import logging
import traceback
from typing import Optional, Callable, Any
from functools import wraps
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError


class ErrorHandler:
    """Centralized error handling for bot operations"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the error handler"""
        self.logger = logger or logging.getLogger(__name__)
    
    async def handle_telegram_error(self, update: Update, context: ContextTypes.DEFAULT_TYPE, error: Exception) -> None:
        """
        Handle Telegram-specific errors with appropriate logging and user feedback.
        
        Args:
            update: Telegram update object
            context: Bot context
            error: Exception that occurred
        """
        self.logger.error(f"Telegram error occurred: {str(error)}", exc_info=True)
        
        # Extract user info for logging
        user_id = None
        username = "Unknown"
        
        try:
            if update and update.effective_user:
                user_id = update.effective_user.id
                username = update.effective_user.username or "Unknown"
        except Exception:
            pass
        
        self.logger.error(f"Error for user {user_id} (@{username}): {str(error)}")
        
        # Send user-friendly error message if possible
        try:
            if update and update.effective_message:
                await update.effective_message.reply_text(
                    "❌ Sorry, something went wrong. Please try again later.",
                    parse_mode=None
                )
        except Exception as reply_error:
            self.logger.error(f"Could not send error message to user: {str(reply_error)}")
    
    async def handle_database_error(self, operation: str, error: Exception, user_id: Optional[int] = None) -> None:
        """
        Handle database-related errors with appropriate logging.
        
        Args:
            operation: Description of the database operation that failed
            error: Exception that occurred
            user_id: Optional user ID for context
        """
        user_context = f" for user {user_id}" if user_id else ""
        self.logger.error(f"Database error during {operation}{user_context}: {str(error)}", exc_info=True)
    
    def safe_execute(self, operation_name: str):
        """
        Decorator for safe execution of operations with standardized error handling.
        
        Args:
            operation_name: Name of the operation for logging
            
        Returns:
            Decorator function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> Any:
                try:
                    return await func(*args, **kwargs)
                except TelegramError as e:
                    self.logger.error(f"Telegram error in {operation_name}: {str(e)}", exc_info=True)
                    return None
                except Exception as e:
                    self.logger.error(f"Unexpected error in {operation_name}: {str(e)}", exc_info=True)
                    return None
            return wrapper
        return decorator
    
    def safe_execute_sync(self, operation_name: str):
        """
        Decorator for safe execution of synchronous operations.
        
        Args:
            operation_name: Name of the operation for logging
            
        Returns:
            Decorator function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    self.logger.error(f"Error in {operation_name}: {str(e)}", exc_info=True)
                    return None
            return wrapper
        return decorator
    
    def log_operation_start(self, operation: str, user_id: Optional[int] = None, **kwargs) -> None:
        """
        Log the start of an operation.
        
        Args:
            operation: Operation name
            user_id: Optional user ID
            **kwargs: Additional context to log
        """
        context_parts = []
        if user_id:
            context_parts.append(f"user_id={user_id}")
        
        for key, value in kwargs.items():
            context_parts.append(f"{key}={value}")
        
        context_str = f" ({', '.join(context_parts)})" if context_parts else ""
        self.logger.info(f"Starting {operation}{context_str}")
    
    def log_operation_success(self, operation: str, user_id: Optional[int] = None, **kwargs) -> None:
        """
        Log successful completion of an operation.
        
        Args:
            operation: Operation name
            user_id: Optional user ID
            **kwargs: Additional context to log
        """
        context_parts = []
        if user_id:
            context_parts.append(f"user_id={user_id}")
        
        for key, value in kwargs.items():
            context_parts.append(f"{key}={value}")
        
        context_str = f" ({', '.join(context_parts)})" if context_parts else ""
        self.logger.info(f"Successfully completed {operation}{context_str}")
    
    def log_operation_failure(self, operation: str, error: Exception, user_id: Optional[int] = None, **kwargs) -> None:
        """
        Log failure of an operation.
        
        Args:
            operation: Operation name
            error: Exception that occurred
            user_id: Optional user ID
            **kwargs: Additional context to log
        """
        context_parts = []
        if user_id:
            context_parts.append(f"user_id={user_id}")
        
        for key, value in kwargs.items():
            context_parts.append(f"{key}={value}")
        
        context_str = f" ({', '.join(context_parts)})" if context_parts else ""
        self.logger.error(f"Failed {operation}{context_str}: {str(error)}", exc_info=True)
    
    def create_error_response(self, error_type: str, user_friendly: bool = True) -> str:
        """
        Create standardized error response messages.
        
        Args:
            error_type: Type of error (database, network, validation, etc.)
            user_friendly: Whether to return user-friendly message
            
        Returns:
            Error message string
        """
        if not user_friendly:
            return f"Error: {error_type}"
        
        error_messages = {
            'database': "❌ Database error. Please try again later.",
            'network': "❌ Network error. Please check your connection and try again.",
            'validation': "❌ Invalid input. Please check your data and try again.",
            'permission': "❌ You don't have permission to perform this action.",
            'not_found': "❌ Requested item not found.",
            'timeout': "❌ Operation timed out. Please try again.",
            'rate_limit': "❌ Too many requests. Please wait a moment and try again.",
            'generic': "❌ Something went wrong. Please try again later."
        }
        
        return error_messages.get(error_type, error_messages['generic'])
