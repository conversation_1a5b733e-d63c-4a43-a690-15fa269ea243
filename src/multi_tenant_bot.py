"""
Multi-tenant bot manager for Telegram bots.
Handles multiple bot instances for different tenants.
"""

import logging
import threading
from config.config import Config
from db.database import Database
from src.utils.subscription_manager import SubscriptionManager
from src.bot import TelegramBot

class MultiTenantBotManager:
    """
    Manages multiple Telegram bot instances for different tenants.

    This class handles:
    - Discovering tenant databases
    - Creating and managing bot instances for each tenant
    - Starting and stopping bot instances
    """

    def __init__(self, config_path=None):
        """
        Initialize the multi-tenant bot manager.

        Args:
            config_path: Optional path to configuration file
        """
        # Create tenant-aware logger for the multi-tenant manager
        self.logger = logging.getLogger("multi_tenant_manager")
        self.config_path = config_path

        # Get MongoDB URI from config.json
        try:
            import json
            with open('./config/config.json', 'r') as f:
                config = json.load(f)
                self.mongo_uri = config.get('database', {}).get('mongo_uri')
                self.logger.info(f"Using MongoDB URI from config.json: {self.mongo_uri}")
        except Exception as e:
            self.logger.warning(f"Could not load MongoDB URI from config.json: {e}")
            self.mongo_uri = 'mongodb://mongodb:27017/'
            self.logger.info(f"Using default MongoDB URI: {self.mongo_uri}")

        self.tenant_bots = {}  # Maps tenant names to bot instances
        self.tenant_configs = {}  # Maps tenant names to config instances
        self.tenant_threads = {}  # Maps tenant names to bot threads

        # Discover tenant databases
        self._discover_tenants()

    def _discover_tenants(self):
        """Discover all tenant databases and create configs for them"""
        self.logger.info("Discovering tenant databases...")

        # Get all tenant names
        tenant_names = Database.get_all_tenant_names(self.mongo_uri)
        self.logger.info(f"Found {len(tenant_names)} tenant databases: {', '.join(tenant_names)}")

        # Create configs for each tenant
        self.tenant_configs = Config.create_for_all_tenants(self.config_path, self.mongo_uri)
        self.logger.info(f"Created configs for {len(self.tenant_configs)} tenants")

    def initialize_bots(self):
        """Initialize bot instances for all tenants"""
        self.logger.info("Initializing bot instances for all tenants...")

        for tenant_name, config in self.tenant_configs.items():
            try:
                # Initialize database for this tenant
                db = Database(self.mongo_uri, tenant_name=tenant_name)

                # Initialize subscription manager
                subscription_manager = SubscriptionManager(config, db)

                # Initialize bot
                bot = TelegramBot(config, subscription_manager)

                # Store bot instance
                self.tenant_bots[tenant_name] = bot

                self.logger.info(f"Initialized bot for tenant: {tenant_name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize bot for tenant {tenant_name}: {e}")

    def start_all_bots(self):
        """Start all bot instances in separate threads"""
        self.logger.info("Starting all bot instances...")

        for tenant_name, bot in self.tenant_bots.items():
            try:
                # Create and start a thread for this bot
                thread = threading.Thread(
                    target=bot.run,
                    name=f"bot-{tenant_name}",
                    daemon=True
                )
                thread.start()

                # Store thread
                self.tenant_threads[tenant_name] = thread

                self.logger.info(f"Started bot for tenant: {tenant_name}")
            except Exception as e:
                self.logger.error(f"Failed to start bot for tenant {tenant_name}: {e}")

    def stop_all_bots(self):
        """Stop all bot instances"""
        self.logger.info("Stopping all bot instances...")

        # Interrupt all bot threads
        for tenant_name, thread in self.tenant_threads.items():
            self.logger.info(f"Stopping bot for tenant: {tenant_name}")

            # Check if thread is alive
            if thread.is_alive():
                # Interrupt the thread
                import ctypes
                ctypes.pythonapi.PyThreadState_SetAsyncExc(
                    ctypes.c_long(thread.ident),
                    ctypes.py_object(KeyboardInterrupt)
                )

                # Wait for thread to terminate
                thread.join(timeout=5)

                if thread.is_alive():
                    self.logger.warning(f"Bot thread for tenant {tenant_name} did not stop gracefully")
                else:
                    self.logger.info(f"Bot thread for tenant {tenant_name} stopped successfully")

    def refresh_tenants(self):
        """
        Refresh the list of tenant databases and update bot instances.
        Call this method if new tenants have been added to the MongoDB server.
        """
        self.logger.info("Refreshing tenant list...")

        # Stop existing bots
        self.stop_all_bots()

        # Clear existing data
        self.tenant_bots = {}
        self.tenant_configs = {}
        self.tenant_threads = {}

        # Rediscover tenants
        self._discover_tenants()

        # Initialize and start bots
        self.initialize_bots()
        self.start_all_bots()

        self.logger.info("Tenant refresh complete")

    def get_bot(self, tenant_name):
        """
        Get a bot instance for a specific tenant.

        Args:
            tenant_name: Name of the tenant

        Returns:
            TelegramBot instance for the tenant or None if not found
        """
        return self.tenant_bots.get(tenant_name)

    def get_all_tenant_names(self):
        """
        Get a list of all tenant names.

        Returns:
            List of tenant names
        """
        return list(self.tenant_configs.keys())
