#!/usr/bin/env python3
"""
Test script to verify that the welcome image is being sent correctly.
This script checks if the welcome image exists and is readable.
"""

import os
import sys

def main():
    # Check if the welcome image exists
    welcome_image_path = os.path.join('assets', 'welcome.webp')

    print(f"Checking for welcome image at: {welcome_image_path}")

    if not os.path.exists(welcome_image_path):
        print(f"ERROR: Welcome image not found at {welcome_image_path}")
        sys.exit(1)

    if not os.path.isfile(welcome_image_path):
        print(f"ERROR: {welcome_image_path} exists but is not a file")
        sys.exit(1)

    # Check if the file is readable
    try:
        file_size = os.path.getsize(welcome_image_path)
        print(f"Welcome image found! File size: {file_size} bytes")

        # Try to open the file
        with open(welcome_image_path, 'rb') as f:
            # Read a small portion to verify it's readable
            data = f.read(1024)
            print(f"Successfully read {len(data)} bytes from the file")

        print("Welcome image is valid and readable!")
        print("\nThe bot should now be able to send this image when a user executes the /start command.")

    except Exception as e:
        print(f"ERROR: Failed to read the welcome image: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
