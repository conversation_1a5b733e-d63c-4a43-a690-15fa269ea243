import argparse
from pymongo import MongoClient
from tabulate import tabulate
from datetime import datetime
import os

# Use environment variable or default to service name for Docker
MONGO_URI = os.getenv('MONGO_URI') or 'mongodb://mongodb:27017/'
DB_NAME = 'admin_panel'  # Changed from 'telegram_bot' to 'admin_panel'

def get_connection():
    client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
    return client, client[DB_NAME]

def create_collections():
    client, db = get_connection()
    
    # Create access_code collection if it doesn't exist
    if 'access_code' not in db.list_collection_names():
        db.create_collection('access_code')
        # Create unique index on code field
        db.access_code.create_index('code', unique=True)
    
    # Update subscriptions collection schema
    if 'subscriptions' not in db.list_collection_names():
        db.create_collection('subscriptions')
        # Create unique index on user_id field
        db.subscriptions.create_index('user_id', unique=True)
        
        # Add validation schema for new fields
        db.command({
            'collMod': 'subscriptions',
            'validator': {
                '$jsonSchema': {
                    'bsonType': 'object',
                    'required': ['user_id', 'access_code'],
                    'properties': {
                        'user_id': {'bsonType': 'int'},
                        'access_code': {'bsonType': 'string'},
                        'username': {'bsonType': 'string'},
                        'email': {'bsonType': 'string'},
                        'whatsapp': {'bsonType': 'string'},
                        'trading_experience': {
                            'bsonType': 'string',
                            'enum': ['beginner', 'intermediate', 'pro']
                        },
                        'subscription_time': {'bsonType': 'date'},
                        'status': {'bsonType': 'string'}
                    }
                }
            }
        })
    
    # Add test access code
    test_codes = ['123456789']
    for code in test_codes:
        try:
            db.access_code.update_one({'code': code}, {'$set': {'code': code}}, upsert=True)
        except Exception as e:
            print(f"Error adding code {code}: {e}")
    
    client.close()
    print("Collections created successfully!")

def clean_collection(collection_name):
    client, db = get_connection()
    
    if collection_name not in db.list_collection_names():
        print(f"Collection {collection_name} not found. Available collections: {', '.join(db.list_collection_names())}")
        client.close()
        return
    
    result = db[collection_name].delete_many({})
    client.close()
    print(f"Cleaned collection: {collection_name}, removed {result.deleted_count} documents")

def show_collection(collection_name):
    client, db = get_connection()
    
    if collection_name not in db.list_collection_names():
        print(f"Collection {collection_name} not found. Available collections: {', '.join(db.list_collection_names())}")
        client.close()
        return
    
    documents = list(db[collection_name].find({}))
    
    if not documents:
        print(f"Collection {collection_name} is empty")
        client.close()
        return
    
    # Convert ObjectId to string for display
    for doc in documents:
        if '_id' in doc:
            doc['_id'] = str(doc['_id'])
    
    # Get all possible keys from all documents
    all_keys = set()
    for doc in documents:
        all_keys.update(doc.keys())
    
    # Create rows with all fields
    rows = []
    for doc in documents:
        row = [doc.get(key, '') for key in all_keys]
        rows.append(row)
    
    print(f"\nContents of {collection_name}:")
    print(tabulate(rows, headers=list(all_keys), tablefmt='psql'))
    client.close()

def clean_all():
    client, db = get_connection()
    db.access_code.delete_many({})
    db.subscriptions.delete_many({})
    client.close()
    print("Cleaned all collections")

def add_codes(codes):
    client, db = get_connection()
    new_count = 0
    
    for code in codes:
        try:
            result = db.access_code.update_one(
                {'code': code}, 
                {'$set': {'code': code}}, 
                upsert=True
            )
            if result.upserted_id:
                new_count += 1
        except Exception as e:
            print(f"Error adding code {code}: {e}")
    
    client.close()
    print(f"Added {new_count} new codes, {len(codes)-new_count} already existed")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='MongoDB management utility')
    parser.add_argument('--clean', metavar='COLLECTION', help='Clean specific collection')
    parser.add_argument('--show', metavar='COLLECTION', help='Show contents of a collection')
    parser.add_argument('--cleanall', action='store_true', help='Clean all collections')
    parser.add_argument('--add', nargs='+', metavar='CODE', help='Add access codes')
    
    args = parser.parse_args()

    if args.clean:
        clean_collection(args.clean)
    elif args.show:
        show_collection(args.show)
    elif args.cleanall:
        clean_all()
    elif args.add:
        add_codes(args.add)
    else:
        create_collections()