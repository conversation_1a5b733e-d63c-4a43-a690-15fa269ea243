#!/usr/bin/env python3
"""
Test script for master user data functionality.
This script demonstrates how to use the master user data functionality.
"""

from db.database import Database
import os
import sys

def main():
    # Get database connection
    mongo_uri = os.getenv('MONGO_URI') or 'mongodb://fluppycoin.com:27017/'
    db = Database(mongo_uri)
    
    # Sync data from subscriptions to master_user_data
    print("Syncing data from subscriptions to master_user_data...")
    success, message = db.sync_master_user_data()
    print(message)
    
    if not success:
        print("Failed to sync data. Exiting.")
        sys.exit(1)
    
    # Get all users from master_user_data
    print("\nGetting all users from master_user_data...")
    users = db.get_all_master_users()
    print(f"Found {len(users)} users")
    
    # Print first user details (if any)
    if users:
        print("\nFirst user details:")
        user = users[0]
        for key, value in user.items():
            if key != '_id':  # Skip MongoDB ID
                print(f"  {key}: {value}")
    
    # Export users to CSV
    csv_path = 'user_data.csv'
    print(f"\nExporting users to {csv_path}...")
    success = db.export_users_to_csv(csv_path)
    
    if success:
        print(f"Successfully exported user data to {csv_path}")
    else:
        print(f"Failed to export user data to {csv_path}")

if __name__ == "__main__":
    main()
