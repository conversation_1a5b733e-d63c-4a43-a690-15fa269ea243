import os
import unittest

class TestMembershipOfferImage(unittest.TestCase):
    def test_membership_offer_image_exists(self):
        """Test if the membership offer image exists"""
        # Check if the membership offer image exists
        membership_offer_image_path = os.path.join('telegram_bot/assets', 'membership_offer.webp')
        self.assertTrue(os.path.exists(membership_offer_image_path), f"Membership offer image not found at {membership_offer_image_path}")
        self.assertTrue(os.path.isfile(membership_offer_image_path), f"Path exists but is not a file: {membership_offer_image_path}")

if __name__ == '__main__':
    unittest.main()
