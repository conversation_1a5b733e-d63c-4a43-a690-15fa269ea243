2025-04-14 08:19:43,109 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'new_registration', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 19, 43, 95231, tzinfo=datetime.timezone.utc)}
2025-04-14 08:19:43,112 - user_interactions - INFO - Callback from user *********: new_registration
2025-04-14 08:19:45,768 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_want_to_join', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 19, 45, 764562, tzinfo=datetime.timezone.utc)}
2025-04-14 08:19:45,770 - user_interactions - INFO - Callback from user *********: reg_want_to_join
2025-04-14 08:19:48,228 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_opened_account', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 19, 48, 223677, tzinfo=datetime.timezone.utc)}
2025-04-14 08:19:48,230 - user_interactions - INFO - Callback from user *********: reg_opened_account
2025-04-14 08:19:50,276 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_start_verification', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 19, 50, 271994, tzinfo=datetime.timezone.utc)}
2025-04-14 08:19:50,279 - user_interactions - INFO - Callback from user *********: reg_start_verification
2025-04-14 08:19:58,526 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_start_verification', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 19, 58, 509216, tzinfo=datetime.timezone.utc)}
2025-04-14 08:19:58,530 - user_interactions - INFO - Callback from user *********: reg_start_verification
2025-04-14 08:20:08,362 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_start_verification', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 20, 8, 348730, tzinfo=datetime.timezone.utc)}
2025-04-14 08:20:08,367 - user_interactions - INFO - Callback from user *********: reg_start_verification
2025-04-14 08:20:12,299 - user_interactions - INFO - User ********* provided valid access code: 127278491
2025-04-14 08:20:27,424 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'pro', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 20, 27, 413732, tzinfo=datetime.timezone.utc)}
2025-04-14 08:20:27,426 - user_interactions - INFO - Callback from user *********: pro
2025-04-14 08:20:37,098 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'back_to_menu', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 20, 37, 93463, tzinfo=datetime.timezone.utc)}
2025-04-14 08:20:37,101 - user_interactions - INFO - Callback from user *********: back_to_menu
2025-04-14 08:21:01,077 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'new_registration', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 1, 65427, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:01,082 - user_interactions - INFO - Callback from user *********: new_registration
2025-04-14 08:21:04,562 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_want_to_join', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 4, 558371, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:04,564 - user_interactions - INFO - Callback from user *********: reg_want_to_join
2025-04-14 08:21:30,524 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'new_registration', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 30, 514178, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:30,527 - user_interactions - INFO - Callback from user *********: new_registration
2025-04-14 08:21:32,369 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_want_to_join', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 32, 365512, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:32,372 - user_interactions - INFO - Callback from user *********: reg_want_to_join
2025-04-14 08:21:52,364 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'new_registration', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 52, 353717, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:52,366 - user_interactions - INFO - Callback from user *********: new_registration
2025-04-14 08:21:54,097 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_want_to_join', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 54, 92602, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:54,099 - user_interactions - INFO - Callback from user *********: reg_want_to_join
2025-04-14 08:21:55,764 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_opened_account', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 55, 760103, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:55,768 - user_interactions - INFO - Callback from user *********: reg_opened_account
2025-04-14 08:21:57,684 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_start_verification', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 21, 57, 681150, tzinfo=datetime.timezone.utc)}
2025-04-14 08:21:57,686 - user_interactions - INFO - Callback from user *********: reg_start_verification
2025-04-14 08:23:02,233 - user_interactions - INFO - User ********* interaction saved for bot **********: {'callback_data': 'reg_start_verification', 'interaction_time': datetime.datetime(2025, 4, 14, 8, 23, 2, 220668, tzinfo=datetime.timezone.utc)}
2025-04-14 08:23:02,238 - user_interactions - INFO - Callback from user *********: reg_start_verification
