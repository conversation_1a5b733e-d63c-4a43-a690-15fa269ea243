#!/usr/bin/env python3
"""
Test script to verify image caching functionality.
"""

import os
import json
from src.utils.image_manager import <PERSON><PERSON>anager

def test_cache_functionality():
    """Test the cache functionality of ImageManager"""
    print("🔍 Testing Image Cache Functionality...")
    print("=" * 50)

    # Initialize ImageManager (using test tenant)
    image_manager = ImageManager(tenant_name="test")

    # Test cache directory creation
    print(f"\n📁 Cache directory: {image_manager.cache_dir}")
    print(f"📄 Cache file: {image_manager.cache_file}")

    if os.path.exists(image_manager.cache_dir):
        print("  ✅ Cache directory exists")
    else:
        print("  ❌ Cache directory does not exist")

    # Test cache loading
    print(f"\n📋 Cache data loaded: {len(image_manager._cache_data)} entries")
    for image_type, paths in image_manager._cache_data.items():
        print(f"  {image_type}: {len(paths)} cached files")
        for path, file_id in paths.items():
            print(f"    {path} -> {file_id}")

    # Test cache methods
    print("\n🧪 Testing cache methods...")

    # Test caching a file_id
    test_image_type = "welcome"
    test_image_path = "assets/welcome.webp"
    test_file_id = "BAADBAADAwADBREAAR4BAAFPVfq2AA"  # Example file_id

    print(f"  Caching test file_id: {test_file_id}")
    image_manager._cache_file_id(test_image_type, test_image_path, test_file_id)

    # Test retrieving cached file_id
    cached_file_id = image_manager._get_cached_file_id(test_image_path)
    print(f"  Retrieved cached file_id: {cached_file_id}")

    if cached_file_id == test_file_id:
        print("  ✅ Cache storage and retrieval working correctly")
    else:
        print("  ❌ Cache storage or retrieval failed")

    # Test cache file persistence
    if os.path.exists(image_manager.cache_file):
        print(f"\n📄 Cache file exists: {image_manager.cache_file}")
        try:
            with open(image_manager.cache_file, 'r') as f:
                cache_content = json.load(f)
                print(f"  Cache file content: {json.dumps(cache_content, indent=2)}")
        except Exception as e:
            print(f"  ❌ Error reading cache file: {e}")
    else:
        print(f"\n❌ Cache file does not exist: {image_manager.cache_file}")

def test_image_paths_and_cache():
    """Test image paths and their potential cache keys"""
    print("\n🖼️  Testing Image Paths and Cache Keys...")
    print("=" * 50)

    image_manager = ImageManager(tenant_name="test")

    for image_type in image_manager.IMAGE_TYPES.keys():
        image_path = image_manager.get_image_path(image_type)
        if image_path:
            print(f"\n📸 {image_type}:")
            print(f"  Path: {image_path}")
            print(f"  Exists: {os.path.exists(image_path)}")

            # Check if there's a cached file_id for this path
            cached_file_id = image_manager._get_cached_file_id(image_path)
            if cached_file_id:
                print(f"  Cached file_id: {cached_file_id}")
            else:
                print(f"  No cached file_id")
        else:
            print(f"\n❌ {image_type}: No image found")

if __name__ == "__main__":
    test_cache_functionality()
    test_image_paths_and_cache()

    print("\n" + "=" * 50)
    print("✅ Image cache testing completed!")
