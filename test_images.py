#!/usr/bin/env python3
"""
Test script to verify that all required images are available and accessible.
"""

import os
import sys
from src.utils.image_manager import <PERSON>Manager

def test_image_availability():
    """Test if all required images are available"""
    print("🔍 Testing Image Availability...")
    print("=" * 50)

    # Initialize ImageManager (using default tenant for testing)
    image_manager = ImageManager(tenant_name="test")

    # Test each image type
    image_types = ['welcome', 'membership_offer', 'claim_membership', 'verify_membership', 'withdrawal', 'support']

    results = {}

    for image_type in image_types:
        print(f"\n📸 Testing {image_type}:")
        image_path = image_manager.get_image_path(image_type)

        if image_path:
            print(f"  ✅ Found: {image_path}")
            # Check if file actually exists and is readable
            if os.path.exists(image_path) and os.path.isfile(image_path):
                file_size = os.path.getsize(image_path)
                print(f"  📊 Size: {file_size:,} bytes")
                results[image_type] = {'status': 'OK', 'path': image_path, 'size': file_size}
            else:
                print(f"  ❌ File not accessible: {image_path}")
                results[image_type] = {'status': 'ERROR', 'path': image_path, 'error': 'File not accessible'}
        else:
            print(f"  ❌ Not found")
            results[image_type] = {'status': 'NOT_FOUND', 'path': None}

    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print("=" * 50)

    success_count = 0
    for image_type, result in results.items():
        status_icon = "✅" if result['status'] == 'OK' else "❌"
        print(f"{status_icon} {image_type}: {result['status']}")
        if result['status'] == 'OK':
            success_count += 1

    print(f"\n🎯 Result: {success_count}/{len(image_types)} images available")

    if success_count == len(image_types):
        print("🎉 All images are available! Your bot should display images correctly.")
        return True
    else:
        print("⚠️  Some images are missing. The bot will fall back to text-only for missing images.")
        return False

def test_image_manager_methods():
    """Test ImageManager methods"""
    print("\n🔧 Testing ImageManager Methods...")
    print("=" * 50)

    image_manager = ImageManager(tenant_name="test")

    # Test get_image_path with valid type
    print("\n📸 Testing get_image_path('welcome'):")
    welcome_path = image_manager.get_image_path('welcome')
    print(f"  Result: {welcome_path}")

    # Test get_image_path with invalid type
    print("\n📸 Testing get_image_path('invalid'):")
    invalid_path = image_manager.get_image_path('invalid')
    print(f"  Result: {invalid_path}")

    # Test IMAGE_TYPES structure
    print("\n📋 IMAGE_TYPES configuration:")
    for image_type, filenames in image_manager.IMAGE_TYPES.items():
        print(f"  {image_type}: {filenames}")

if __name__ == "__main__":
    print("🤖 Telegram Bot Image System Test")
    print("=" * 50)

    # Test image availability
    images_ok = test_image_availability()

    # Test ImageManager methods
    test_image_manager_methods()

    print("\n" + "=" * 50)
    if images_ok:
        print("🎉 All tests passed! Your image system should work correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some issues found. Check the output above for details.")
        sys.exit(1)
