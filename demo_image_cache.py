#!/usr/bin/env python3
"""
Demo script showing image caching functionality.
This simulates how the caching works without actually sending to Telegram.
"""

import os
import json
from src.utils.image_manager import ImageManager

def simulate_telegram_response(image_path):
    """Simulate a Telegram response with a file_id"""
    # In real usage, this would be the actual Telegram response
    # For demo, we'll generate a fake file_id based on the image path
    import hashlib
    fake_file_id = "BAAD" + hashlib.md5(image_path.encode()).hexdigest()[:20].upper()

    class FakePhoto:
        def __init__(self, file_id):
            self.file_id = file_id

    class FakeMessage:
        def __init__(self, file_id):
            self.photo = [FakePhoto(file_id)]  # Simulate photo array

    return FakeMessage(fake_file_id)

def demo_caching_workflow():
    """Demonstrate the complete caching workflow"""
    print("🚀 Image Caching Demo")
    print("=" * 50)

    # Initialize ImageManager (using demo tenant)
    image_manager = ImageManager(tenant_name="demo")

    # Clear any existing cache for clean demo
    if os.path.exists(image_manager.cache_file):
        os.remove(image_manager.cache_file)
        image_manager._cache_data = {}
        print("🧹 Cleared existing cache for clean demo")

    # Demo images to test
    demo_images = ['welcome', 'membership_offer', 'support']

    print(f"\n📸 Demo Images: {demo_images}")
    print("\n" + "=" * 50)

    # First round: Simulate sending images (cache miss)
    print("🔄 ROUND 1: First time sending (cache miss)")
    print("-" * 30)

    for image_type in demo_images:
        image_path = image_manager.get_image_path(image_type)
        if image_path:
            print(f"\n📤 Sending {image_type} ({image_path})")

            # Check cache (should be empty)
            cached_file_id = image_manager._get_cached_file_id(image_path)
            if cached_file_id:
                print(f"  ✅ Cache HIT: {cached_file_id}")
            else:
                print(f"  ❌ Cache MISS: No cached file_id")

                # Simulate sending from disk and getting response
                print(f"  📁 Sending from disk: {image_path}")
                fake_response = simulate_telegram_response(image_path)
                file_id = fake_response.photo[-1].file_id

                # Cache the file_id
                image_manager._cache_file_id(image_type, image_path, file_id)
                print(f"  💾 Cached file_id: {file_id}")

    # Show cache state
    print(f"\n📋 Cache state after round 1:")
    with open(image_manager.cache_file, 'r') as f:
        cache_content = json.load(f)
        print(json.dumps(cache_content, indent=2))

    # Second round: Simulate sending same images (cache hit)
    print("\n" + "=" * 50)
    print("🔄 ROUND 2: Sending same images (cache hit)")
    print("-" * 30)

    for image_type in demo_images:
        image_path = image_manager.get_image_path(image_type)
        if image_path:
            print(f"\n📤 Sending {image_type} ({image_path})")

            # Check cache (should have file_id now)
            cached_file_id = image_manager._get_cached_file_id(image_path)
            if cached_file_id:
                print(f"  ✅ Cache HIT: {cached_file_id}")
                print(f"  ⚡ Sending using cached file_id (FAST!)")
            else:
                print(f"  ❌ Cache MISS: This shouldn't happen!")

    # Demo cache invalidation scenario
    print("\n" + "=" * 50)
    print("🔄 ROUND 3: Simulating cache invalidation")
    print("-" * 30)

    # Simulate first image having invalid cached file_id
    first_image_type = demo_images[0]
    first_image_path = image_manager.get_image_path(first_image_type)

    print(f"\n📤 Sending {first_image_type} (simulating invalid cached file_id)")
    cached_file_id = image_manager._get_cached_file_id(first_image_path)
    print(f"  📋 Cached file_id: {cached_file_id}")
    print(f"  ❌ Simulating: Cached file_id is invalid/expired")
    print(f"  📁 Fallback: Sending from disk")

    # Simulate new response with different file_id
    new_fake_response = simulate_telegram_response(first_image_path + "_new")
    new_file_id = new_fake_response.photo[-1].file_id
    image_manager._cache_file_id(first_image_type, first_image_path, new_file_id)
    print(f"  💾 Updated cache with new file_id: {new_file_id}")

    # Final cache state
    print(f"\n📋 Final cache state:")
    with open(image_manager.cache_file, 'r') as f:
        cache_content = json.load(f)
        print(json.dumps(cache_content, indent=2))

    print("\n" + "=" * 50)
    print("✅ Demo completed!")
    print("\n🎯 Key Benefits Demonstrated:")
    print("  • Cache MISS: First send uploads from disk + caches file_id")
    print("  • Cache HIT: Subsequent sends use cached file_id (much faster)")
    print("  • Cache INVALIDATION: Automatic fallback and cache update")
    print("  • PERSISTENCE: Cache survives between bot restarts")

if __name__ == "__main__":
    demo_caching_workflow()
