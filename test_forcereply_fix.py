#!/usr/bin/env python3
"""
Test script to verify the ForceReply import fix.
"""

def test_forcereply_import():
    """Test that ForceReply can be imported and used correctly"""
    print("🔧 Testing ForceReply Import Fix")
    print("=" * 40)
    
    try:
        # Test the import that was causing issues
        from telegram import ForceReply
        print("✅ ForceReply imported successfully")
        
        # Test creating a ForceReply instance
        force_reply = ForceReply(selective=True)
        print("✅ ForceReply instance created successfully")
        print(f"   ForceReply object: {force_reply}")
        
        # Test that it has the expected attributes
        if hasattr(force_reply, 'force_reply'):
            print("✅ ForceReply has force_reply attribute")
        else:
            print("❌ ForceReply missing force_reply attribute")
            
        if hasattr(force_reply, 'selective'):
            print("✅ ForceReply has selective attribute")
        else:
            print("❌ ForceReply missing selective attribute")
            
    except Exception as e:
        print(f"❌ Error with ForceReply: {e}")
        return False
    
    return True

def test_callback_handlers_import():
    """Test that CallbackHandlers can be imported without issues"""
    print("\n🔧 Testing CallbackHandlers Import")
    print("=" * 40)
    
    try:
        # Test importing the fixed module
        from src.handlers.callback_handlers import CallbackHandlers
        print("✅ CallbackHandlers imported successfully")
        
        # Test that ForceReply is available in the module scope
        import src.handlers.callback_handlers as cb_module
        if hasattr(cb_module, 'ForceReply'):
            print("✅ ForceReply is available in CallbackHandlers module")
        else:
            print("❌ ForceReply not available in CallbackHandlers module")
            
    except Exception as e:
        print(f"❌ Error importing CallbackHandlers: {e}")
        return False
    
    return True

def test_image_manager_integration():
    """Test that ImageManager integration works"""
    print("\n🔧 Testing ImageManager Integration")
    print("=" * 40)
    
    try:
        from src.utils.image_manager import ImageManager
        
        # Test creating ImageManager with tenant
        image_manager = ImageManager(tenant_name="test_fix")
        print("✅ ImageManager created with tenant successfully")
        
        # Test account_number image type
        account_image_path = image_manager.get_image_path('account_number')
        if account_image_path:
            print(f"✅ Account number image path: {account_image_path}")
        else:
            print("❌ Account number image path not found")
            
    except Exception as e:
        print(f"❌ Error with ImageManager: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Testing ForceReply Fix and Integration")
    print("=" * 50)
    
    success = True
    
    # Run all tests
    success &= test_forcereply_import()
    success &= test_callback_handlers_import()
    success &= test_image_manager_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed! The ForceReply fix is working correctly.")
        print("\n🎯 Key fixes applied:")
        print("  • Removed conflicting local ForceReply import")
        print("  • Refactored hardcoded account number image handling")
        print("  • Integrated account_number image with caching system")
        print("  • Cleaned up unused imports")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("\n🔧 The bot should now work without ForceReply errors!")
