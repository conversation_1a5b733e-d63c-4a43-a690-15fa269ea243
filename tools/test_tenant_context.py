#!/usr/bin/env python3
"""
Test script for tenant context functionality.
This script demonstrates how to:
1. Connect to MongoDB and list all tenant databases
2. Create a database connection for a specific tenant
3. Retrieve bot configuration from a tenant database
4. Test the tenant context functionality
"""

import sys
import logging
import json
import os

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from db.database import Database
from db.tenant_manager import TenantDatabaseManager
from config.config import Config
from src.utils.subscription_manager import SubscriptionManager

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def get_mongo_uri():
    """Get MongoDB URI from config.json"""
    try:
        with open('./config/config.json', 'r') as f:
            config = json.load(f)
            mongo_uri = config.get('database', {}).get('mongo_uri')
            return mongo_uri
    except Exception as e:
        print(f"Could not load MongoDB URI from config.json: {e}")
        return 'mongodb://mongodb:27017/'

def test_tenant_context(tenant_name=None):
    """
    Test the tenant context functionality.
    
    Args:
        tenant_name: Optional name of the tenant to test
    """
    logger = setup_logging()
    mongo_uri = get_mongo_uri()
    
    logger.info(f"Using MongoDB URI: {mongo_uri}")
    
    try:
        # Initialize tenant manager
        tenant_manager = TenantDatabaseManager(mongo_uri)
        
        # Get all tenant names
        tenant_names = tenant_manager.get_all_tenant_names()
        logger.info(f"Discovered tenant databases: {', '.join(tenant_names)}")
        
        # If a specific tenant is specified, only test that one
        if tenant_name:
            if tenant_name not in tenant_names:
                logger.error(f"Tenant '{tenant_name}' not found in discovered tenants")
                return
            tenant_names = [tenant_name]
        
        # Test each tenant
        for name in tenant_names:
            logger.info(f"Testing tenant context for: {name}")
            
            # Create config for this tenant
            config = Config(tenant_name=name)
            
            # Create database for this tenant
            db = Database(mongo_uri, tenant_name=name)
            
            # Create subscription manager
            sub_manager = SubscriptionManager(config, db)
            
            # Create a mock context object
            class MockContext:
                def __init__(self):
                    self.bot_data = {'tenant_name': name}
            
            context = MockContext()
            
            # Test getting tenant name from context
            tenant_name_from_context = sub_manager.get_tenant_name_from_context(context)
            logger.info(f"Tenant name from context: {tenant_name_from_context}")
            
            # Test getting tenant database from context
            tenant_db = sub_manager.get_tenant_database(context)
            logger.info(f"Tenant database from context: {tenant_db}")
            
            # Test getting master user data
            try:
                # Get a sample user ID from the master_user_data collection
                user_docs = list(db.db.master_user_data.find().limit(1))
                if user_docs:
                    user_id = user_docs[0].get('user_id')
                    logger.info(f"Found user ID: {user_id}")
                    
                    # Get user data using the tenant context
                    user_data = sub_manager.get_master_user_data(user_id, context)
                    logger.info(f"User data from tenant context: {user_data is not None}")
                else:
                    logger.warning(f"No users found in tenant database: {name}")
            except Exception as e:
                logger.error(f"Error getting user data: {e}")
            
            logger.info(f"Tenant context test completed for: {name}")
            logger.info("-" * 50)
        
    except Exception as e:
        logger.error(f"Error testing tenant context: {e}")

def main():
    """Main entry point for the test script"""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test tenant context functionality')
    parser.add_argument('--tenant', help='Name of the tenant to test')
    args = parser.parse_args()
    
    # Test tenant context
    test_tenant_context(args.tenant)

if __name__ == "__main__":
    main()
