#!/usr/bin/env python3
"""
Test script for running a Telegram bot in a thread.
This script demonstrates how to:
1. Create a bot instance
2. Run the bot in a separate thread
3. Keep the main thread alive
"""

import sys
import time
import logging
import threading
import json

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from config.config import Config
from db.database import Database
from src.utils.subscription_manager import SubscriptionManager
from src.bot import TelegramBot

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def get_mongo_uri():
    """Get MongoDB URI from config.json"""
    try:
        with open('./config/config.json', 'r') as f:
            config = json.load(f)
            mongo_uri = config.get('database', {}).get('mongo_uri')
            return mongo_uri
    except Exception as e:
        print(f"Could not load MongoDB URI from config.json: {e}")
        return 'mongodb://mongodb:27017/'

def run_bot_in_thread(tenant_name=None):
    """
    Run a Telegram bot in a separate thread.
    
    Args:
        tenant_name: Optional name of the tenant
    """
    logger = setup_logging()
    mongo_uri = get_mongo_uri()
    
    logger.info(f"Using MongoDB URI: {mongo_uri}")
    
    try:
        # Initialize configuration
        if tenant_name:
            logger.info(f"Initializing configuration for tenant: {tenant_name}")
            config = Config(tenant_name=tenant_name)
        else:
            logger.info("Initializing configuration in single-tenant mode")
            config = Config()
        
        # Initialize database
        if tenant_name:
            logger.info(f"Initializing database for tenant: {tenant_name}")
            db = Database(mongo_uri, tenant_name=tenant_name)
        else:
            logger.info("Initializing database in single-tenant mode")
            db = Database(mongo_uri)
        
        # Initialize subscription manager
        logger.info("Initializing subscription manager")
        subscription_manager = SubscriptionManager(config, db)
        
        # Initialize bot
        logger.info("Initializing Telegram bot")
        bot = TelegramBot(config, subscription_manager)
        
        # Create and start a thread for the bot
        logger.info("Starting bot in a separate thread")
        thread = threading.Thread(
            target=bot.run,
            name=f"bot-{tenant_name or 'main'}",
            daemon=True
        )
        thread.start()
        
        # Keep the main thread alive
        logger.info("Bot thread started. Main thread will stay alive for 60 seconds.")
        time.sleep(60)
        
        logger.info("Test completed")
        
    except Exception as e:
        logger.error(f"Error running bot in thread: {e}", exc_info=True)

def main():
    """Main entry point for the test script"""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test running a Telegram bot in a thread')
    parser.add_argument('--tenant', help='Name of the tenant to use')
    args = parser.parse_args()
    
    # Run bot in thread
    run_bot_in_thread(args.tenant)

if __name__ == "__main__":
    main()
