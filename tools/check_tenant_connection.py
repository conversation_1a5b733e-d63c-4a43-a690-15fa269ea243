#!/usr/bin/env python3
"""
Tool for checking the connection to a tenant database.
This script:
1. Connects to MongoDB
2. Discovers tenant databases
3. Attempts to retrieve bot configuration from each tenant
"""

import sys
import logging
import json

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from db.tenant_manager import TenantDatabaseManager
from db.database import Database

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def get_mongo_uri():
    """Get MongoDB URI from config.json"""
    try:
        with open('./config/config.json', 'r') as f:
            config = json.load(f)
            mongo_uri = config.get('database', {}).get('mongo_uri')
            return mongo_uri
    except Exception as e:
        print(f"Could not load MongoDB URI from config.json: {e}")
        return 'mongodb://mongodb:27017/'

def check_tenant_connection(tenant_name=None):
    """
    Check connection to a tenant database.
    
    Args:
        tenant_name: Optional name of the tenant to check
    """
    logger = setup_logging()
    mongo_uri = get_mongo_uri()
    
    logger.info(f"Using MongoDB URI: {mongo_uri}")
    
    try:
        # Initialize tenant manager
        tenant_manager = TenantDatabaseManager(mongo_uri)
        
        # Get all tenant names
        tenant_names = tenant_manager.get_all_tenant_names()
        logger.info(f"Discovered tenant databases: {', '.join(tenant_names)}")
        
        # If a specific tenant is specified, only check that one
        if tenant_name:
            if tenant_name not in tenant_names:
                logger.error(f"Tenant '{tenant_name}' not found in discovered tenants")
                return
            tenant_names = [tenant_name]
        
        # Check each tenant
        for name in tenant_names:
            logger.info(f"Checking tenant: {name}")
            
            # Get database for this tenant
            db = Database(mongo_uri, tenant_name=name)
            
            # Try to get bot info
            try:
                bot_info = db.get_telegram_bot_info()
                if bot_info is None:
                    logger.error(f"No bot information found for tenant: {name}")
                else:
                    logger.info(f"Successfully retrieved bot info for tenant: {name}")
                    logger.info(f"  Bot name: {bot_info.get('name', 'Unknown')}")
                    logger.info(f"  Bot username: {bot_info.get('username', 'Unknown')}")
                    logger.info(f"  Channel ID: {bot_info.get('channel_id', 'Unknown')}")
            except Exception as e:
                logger.error(f"Error retrieving bot info for tenant {name}: {e}")
        
    except Exception as e:
        logger.error(f"Error checking tenant connection: {e}")

def main():
    """Main entry point for the tool"""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Check connection to tenant databases')
    parser.add_argument('--tenant', help='Name of the tenant to check')
    args = parser.parse_args()
    
    # Check tenant connection
    check_tenant_connection(args.tenant)

if __name__ == "__main__":
    main()
